package burst

// import (
// 	"context"
// 	"encoding/json"
// 	"errors"
// 	"fmt"
// 	"log"
// 	"os"
// 	"runtime"
// 	"runtime/debug"
// 	"sort"
// 	"strings"
// 	"sync"
// 	"time"

// 	"futures-asset/cache"
// 	"futures-asset/cache/cachekey"
// 	"futures-asset/cache/cachelock"
// 	"futures-asset/cache/price"
// 	"futures-asset/cache/sharedcache"
// 	"futures-asset/cache/swapcache"
// 	"futures-asset/handler/monitor"
// 	"futures-asset/internal/domain"
// 	"futures-asset/internal/domain/repository"
// 	"futures-asset/pkg/match"
// 	"futures-asset/pkg/operate"
// 	"futures-asset/pkg/setting"
// 	"futures-asset/util"

// 	"github.com/redis/go-redis/v9"
// 	"github.com/shopspring/decimal"
// 	"github.com/sirupsen/logrus"
// )

// type burstingData struct {
// 	sync.Mutex
// 	WorkingMap map[string]string
// }

// var (
// 	bursting       *burstingData
// 	recoverWorking *sync.Mutex
// )

// func init() {
// 	bursting = new(burstingData)
// 	bursting.WorkingMap = make(map[string]string)
// 	recoverWorking = new(sync.Mutex)
// }

// func ProcessorStart(ctx context.Context, wg *sync.WaitGroup, isRecover bool) {
// 	defer func() {
// 		if err := recover(); err != nil {
// 			logrus.Error(0, "ProcessorStart recover error:", err)
// 			time.Sleep(time.Second * 5)
// 			ProcessorStart(ctx, wg, true)
// 		}
// 	}()
// 	if !isRecover {
// 		wg.Add(1)
// 	}
// 	secondTicker := time.NewTicker(time.Second)
// Loop:
// 	for {
// 		select {
// 		case <-secondTicker.C:
// 			// 检查是否新增币对
// 			burstContracts := sharedcache.GetBurstServerContracts()
// 			if len(burstContracts) < 1 {
// 				log.Fatalf("burst contracts is empty")
// 			}
// 			sort.Strings(burstContracts)
// 			// 快照币对列表
// 			snapProcessorContracts.Lock()
// 			memSnapContracts := snapProcessorContracts.List
// 			snapProcessorContracts.Unlock()
// 			sort.Strings(memSnapContracts)
// 			burstContractsStr := strings.Join(burstContracts, "|")
// 			snapContractsStr := strings.Join(memSnapContracts, "|")
// 			// 配置币对对比内存快照币对
// 			if burstContractsStr != snapContractsStr {
// 				// 配置币对map
// 				burstContractMap := map[string]string{}
// 				for _, symbol := range burstContracts {
// 					burstContractMap[symbol] = symbol
// 				}
// 				// 快照币对map
// 				memContractMap := map[string]string{}
// 				for _, symbol := range memSnapContracts {
// 					memContractMap[symbol] = symbol
// 				}
// 				// 确定移除币对
// 				var removeSymbolList []string
// 				for _, symbol := range memSnapContracts {
// 					if _, ok := burstContractMap[symbol]; !ok {
// 						removeSymbolList = append(removeSymbolList, symbol)
// 					}
// 				}
// 				// 确定添加币对
// 				var addSymbolList []string
// 				for _, symbol := range burstContracts {
// 					if _, ok := memContractMap[symbol]; !ok {
// 						addSymbolList = append(addSymbolList, symbol)
// 					}
// 				}
// 				// 移除币对
// 				processorStop(removeSymbolList)
// 				snapProcessorContracts.Lock()
// 				snapProcessorContracts.List = burstContracts
// 				snapProcessorContracts.Unlock()
// 				// 增加币对
// 				for _, symbol := range addSymbolList {
// 					hasContext := false
// 					snapProcessorContext.Lock()
// 					_, hasContext = snapProcessorContext.Map[symbol]
// 					snapProcessorContext.Unlock()
// 					if !hasContext {
// 						go _processorStart(ctx, wg, symbol, false)
// 					}
// 				}
// 			}

// 		case <-ctx.Done():
// 			break Loop

// 		}
// 		runtime.Gosched()
// 	}

// 	wg.Done()
// }

// func _processorStart(ctx context.Context, wg *sync.WaitGroup, symbol string, isRecover bool) {
// 	defer func() {
// 		if err := recover(); err != nil {
// 			logrus.Error(0, strings.ToUpper(symbol), "processorStart recover error:", err)
// 			time.Sleep(time.Second * 5)
// 			_processorStart(ctx, wg, symbol, true)
// 		}
// 	}()

// 	var burstWorker *workingBurstService = nil
// 	if !isRecover {
// 		burstWorker = buildService(symbol)
// 	} else {
// 		RunningServices.RLock()
// 		burstWorker = RunningServices.Map[strings.ToUpper(symbol)]
// 		RunningServices.RUnlock()
// 	}
// 	if burstWorker == nil {
// 		return
// 	}

// 	err := burstWorker.updateServiceCoinPairInfo()
// 	if err != nil {
// 		logrus.Error(0, strings.ToUpper(symbol), "processorStart updateServiceCoinPairInfo error:", err)
// 	}

// 	// 恢复爆仓记录
// 	burstWorker.RecoverBurstList()

// 	_wg := new(sync.WaitGroup)
// 	// 启动爆仓处理
// 	burstProcessorCtx, burstProcessorCtxCancel := context.WithCancel(ctx)
// 	processNum := 6

// 	go burstWorker.NewBurstProcessorStart(processNum, burstProcessorCtx, _wg)

// 	// 启动币对平仓处理
// 	cleanUserPosProcessorCtx, cleanUserPosProcessorCtxCancel := context.WithCancel(ctx)
// 	go burstWorker._cleanUserPosProcessor(cleanUserPosProcessorCtx, _wg, false)

// 	// 追加内存快照
// 	RunningServices.Lock()
// 	RunningServices.Map[strings.ToUpper(symbol)] = burstWorker
// 	RunningServices.Unlock()
// 	if !isRecover {
// 		wg.Add(1)
// 		snapProcessorContracts.Lock()
// 		snapProcessorContracts.List = append(snapProcessorContracts.List, symbol)
// 		snapProcessorContracts.Unlock()
// 	}
// 	snapProcessorContext.Lock()
// 	if funcList, ok := snapProcessorContext.Map[symbol]; ok {
// 		for _, cancelFunc := range funcList {
// 			cancelFunc()
// 		}
// 	}
// 	snapProcessorContext.Map[symbol] = []context.CancelFunc{burstProcessorCtxCancel, cleanUserPosProcessorCtxCancel}

// 	snapProcessorContext.Unlock()
// 	logrus.Info(0, symbol, "burst processor started")

// 	<-burstProcessorCtx.Done()
// 	<-cleanUserPosProcessorCtx.Done()
// 	time.Sleep(time.Second * 3)
// 	_wg.Wait()
// 	burstWorker = nil
// 	logrus.Info(0, symbol, "burst processor stopped")
// 	wg.Done()
// }

// // RecoverBurstList 恢复未完成的爆仓任务
// //
// //	可能会无法完全爆仓
// //
// // Deprecated
// func (bs *workingBurstService) RecoverBurstList() {
// 	if tryLocked := recoverWorking.TryLock(); !tryLocked {
// 		return
// 	}
// 	defer recoverWorking.Unlock()

// 	bs._fromRecoverListToOriginBurstList(domain.MarginModeCross)

// 	bs._fromRecoverListToOriginBurstList(domain.MarginModeIsolated)

// 	return
// }

// // Deprecated
// func (bs *workingBurstService) _fromRecoverListToOriginBurstList(marginMode domain.MarginMode) {
// 	recoverListKey := cachekey.GetRecoverBurstListRedisKey(bs.contractCode, marginMode)
// 	originBurstListKey := cachekey.GetBurstListRedisKey(bs.contractCode, marginMode)
// 	marginModeDataMap, err := redisCli.HGetAll(recoverListKey)
// 	if err != nil {
// 		logrus.Error(0, "fromRecoverListToOriginBurstList HGetAll", recoverListKey, "error:", err)
// 		return
// 	}

// 	for k, v := range marginModeDataMap {
// 		err = redisCli.LPush(originBurstListKey, v)
// 		if err != nil {
// 			logrus.Error(0, "fromRecoverListToOriginBurstList LPush", originBurstListKey, "error:", err)
// 			return
// 		}
// 		err = redisCli.HDel(recoverListKey, k)
// 		if err != nil {
// 			logrus.Error(0, "RecoverBurstList HDel", recoverListKey, k, "error:", err)
// 			return
// 		}
// 	}
// }

// // Deprecated
// func (bs *workingBurstService) _saveRecoverTaskList(uid, burstId string, marginMode domain.MarginMode, burstTask string) {
// 	recoverKey := cachekey.GetRecoverBurstListRedisKey(bs.contractCode, marginMode)
// 	// 记录爆仓恢复数据
// 	err := redisCli.HSet(recoverKey, burstId, burstTask)
// 	if err != nil {
// 		if err != redis.Nil {
// 			logrus.Error(0, bs.contractCode, uid, burstId, "burstProcessor saveRecoverTaskList error:", err)
// 		}
// 	}
// 	return
// }

// // Deprecated
// func (bs *workingBurstService) _deleteRecoverTaskList(uid, burstId string, marginMode domain.MarginMode) {
// 	recoverKey := cachekey.GetRecoverBurstListRedisKey(bs.contractCode, marginMode)
// 	// 记录爆仓恢复数据
// 	err := redisCli.HDel(recoverKey, burstId)
// 	if err != nil {
// 		if err != redis.Nil {
// 			logrus.Error(0, bs.contractCode, uid, burstId, "burstProcessor deleteRecoverTaskList error:", err)
// 		}
// 	}
// 	return
// }

// // BurstProcessorStart 旧爆仓
// // Deprecated
// func (bs *workingBurstService) BurstProcessorStart(processor int, ctx context.Context, wg *sync.WaitGroup, _isRecover bool) {
// 	defer func() {
// 		if err := recover(); err != nil {
// 			time.Sleep(time.Second * 5)
// 			bs.BurstProcessorStart(processor, ctx, wg, true)
// 		}
// 	}()
// 	if !_isRecover {
// 		wg.Add(1)
// 	}
// 	_wg := new(sync.WaitGroup)
// 	for i := 0; i < processor; i++ {
// 		go bs._burstProcessor(i, domain.MarginModeCross, ctx, _wg, false)
// 		go bs._burstProcessor(i, domain.MarginModeIsolated, ctx, _wg, false)
// 	}

// 	time.Sleep(time.Second * 5)
// Loop:
// 	for {
// 		select {
// 		case <-ctx.Done():
// 			break Loop
// 		}
// 	}

// 	bs.burstStop = true
// 	_wg.Wait()
// 	wg.Done()
// 	fmt.Println(bs.contractCode, "BurstProcessor all stopped")
// }

// // Deprecated
// func (bs *workingBurstService) _getBurstingTask(marginMode domain.MarginMode, workingIndex string) (string, error) {
// 	indexTaskKey := cachekey.GetBurstIndexListRedisKey(bs.contractCode, marginMode, workingIndex)
// 	originTaskKey := cachekey.GetBurstListRedisKey(bs.contractCode, marginMode)

// 	// 拉取索引爆仓任务
// 	jsonStr, err := redisCli.RPop(indexTaskKey)
// 	if err != nil {
// 		if err == redis.Nil {
// 			// 拉取爆仓原始任务
// 			dataList, err := redisCli.BRPop(originTaskKey, time.Second*10)
// 			if err != nil {
// 				if err == redis.Nil {
// 					// bs.RecoverBurstList(bs.contractCode, _marginMode)
// 				} else {
// 					logrus.Error(0, bs.contractCode, "burstProcessor RPop", originTaskKey, "error:", err)
// 				}
// 				return "", err
// 			}
// 			if len(dataList) == 2 {
// 				jsonStr = dataList[1]
// 			}
// 		}
// 	}
// 	return jsonStr, nil
// }

// // Deprecated
// func (bs *workingBurstService) _verifyBurstTask(workingIndex, burstTaskJson string) (*burstPosInfo, string, error) {
// 	_burstPosInfo := new(burstPosInfo)
// 	lockBurstId := ""
// 	// 非空字符串
// 	if len(burstTaskJson) > 0 {
// 		err := json.Unmarshal([]byte(burstTaskJson), _burstPosInfo)
// 		if err != nil {
// 			logrus.Error(0, bs.contractCode, "burstProcessor Unmarshal error:", err, burstTaskJson)
// 			return _burstPosInfo, "", err
// 		}
// 		// 解析之后无userId
// 		if len(_burstPosInfo.UID) < 1 {
// 			logrus.Error(0, bs.contractCode, "burstProcessor error task data:", burstTaskJson)
// 			return _burstPosInfo, "", err
// 		}

// 		// 如果没有爆仓锁获取到爆仓属于错误
// 		lockParams := repository.BurstLockParam{
// 			Liquidation: cache.LiquidationInfo{
// 				UID:        _burstPosInfo.UID,
// 				MarginMode: _burstPosInfo.MarginMode,
// 			},
// 		}

// 		userLockedByBurst := false
// 		// 获取是有爆仓锁
// 		userLockedByBurst, err = cachelock.BurstUserIsLocked(lockParams)
// 		if err != nil {
// 			logrus.Error(0, bs.contractCode, "burstProcessor BurstUserIsLocked error:", err, fmt.Sprintf("%+v", lockParams))
// 			return _burstPosInfo, "", err
// 		}

// 		switch _burstPosInfo.MarginMode {
// 		case domain.MarginModeIsolated:
// 			lockParams.Liquidation.PosSide = _burstPosInfo.PosSide
// 			fallthrough

// 		case domain.MarginModeCross:
// 			lockParams.ContractCode = _burstPosInfo.ContractCode()

// 		default:

// 		}

// 		symbolLockedByBurst := true

// 		// 获取爆仓锁的爆仓id
// 		_, lockBurstId, err = cachelock.GetBurstLockInfo(lockParams)
// 		if err != nil {
// 			logrus.Error(0, bs.contractCode, "burstProcessor GetBurstLockInfo error:", err, fmt.Sprintf("%+v", lockParams))
// 			return _burstPosInfo, "", err
// 		}

// 		// 如果爆仓锁
// 		if len(lockBurstId) < 1 {
// 			symbolLockedByBurst = false
// 		} else {
// 			// 锁定的爆仓id不等于当前爆仓id
// 			if _burstPosInfo.BurstId != lockBurstId {
// 				symbolLockedByBurst = false
// 			}
// 		}

// 		if err != nil {
// 			logrus.Error(0, bs.contractCode, "burstProcessor error:", err)
// 			return _burstPosInfo, "", err
// 		}

// 		// 不是被锁定的爆仓币对
// 		if !symbolLockedByBurst {
// 			// 检查是否爆仓结束
// 			if userLockedByBurst {
// 				// 用户仓位
// 				crossPos, _ := make([]repository.PosSwap, 0), make([]repository.PosSwap, 0)
// 				// 获取用户资产信息
// 				userCache, assetInfo, err := swapcache.GetUserCacheData(_burstPosInfo.Base, _burstPosInfo.Quote, _burstPosInfo.UID)
// 				if err != nil {
// 					logrus.Error(0, _burstPosInfo.ContractCode(), _burstPosInfo.UID, "burstService burstProcessor GetUserCacheData error:", err)
// 					bs._relineBurstTask(_burstPosInfo, workingIndex)

// 				} else {
// 					pCache := price.New()
// 					if assetInfo.AssetMode == futuresassetpb.AssetMode_ASSET_MODE_SINGLE {
// 						crossPos, _, err = userCache.UserPos(false, bs.quote, pCache)
// 					} else {
// 						crossPos, _, err = userCache.UserPos(true, "", pCache)
// 					}
// 					if err != nil {
// 						logrus.Error(0, bs.contractCode, "burstProcessor verifyBurstTask userCache.UserPos error:", err, _burstPosInfo.UID)
// 						bs._relineBurstTask(_burstPosInfo, workingIndex)
// 					} else {
// 						if len(crossPos) < 1 {
// 							bs._checkBurstFinish(assetInfo, crossPos, _burstPosInfo)
// 						} else {
// 							for _, posInfo := range crossPos {
// 								if posInfo.ContractCode == _burstPosInfo.ContractCode() {
// 									// 全仓币对锁丢失，补全仓币对锁
// 									// 增加全仓币对爆仓锁
// 									err := cachelock.LockBurstSymbol(repository.BurstLockParam{
// 										ContractCode: posInfo.ContractCode,
// 										Liquidation: cache.LiquidationInfo{
// 											BurstId:    _burstPosInfo.BurstId,
// 											UID:        userCache.UID,
// 											MarginMode: _burstPosInfo.MarginMode,
// 										},
// 									})
// 									if err != nil {
// 										logrus.Error(0, "burstProcessor verifyBurstTask LockBurstUser", userCache.UID, "error:", err)
// 									}
// 									bs._relineBurstTask(_burstPosInfo, workingIndex)
// 								}
// 							}
// 						}
// 					}
// 				}

// 			} else {
// 				logrus.Error(0, _burstPosInfo.ContractCode(), "burstProcessor no user lock error data:", burstTaskJson)
// 				// 清楚恢复列表爆仓信息
// 				bs._deleteRecoverTaskList(_burstPosInfo.UID, _burstPosInfo.BurstId, _burstPosInfo.MarginMode)
// 			}
// 			// 清除工作中标记
// 			bs._cleanWorkingFlag(_burstPosInfo.UID, _burstPosInfo.PosId)

// 			return nil, "", errors.New("burstProcessor no symbol lock error")
// 		}
// 	}
// 	return _burstPosInfo, lockBurstId, nil
// }

// // Deprecated
// func (bs *workingBurstService) genWorkingFlag(uid, posId string) string {
// 	flag := fmt.Sprintf("%s_%s", uid, posId)
// 	return flag
// }

// // _burstProcessor 爆仓处理器
// //
// //	爆仓处理主流程逻辑
// //	Params:
// //	  _index int: 处理协程索引
// //	  _ctx context.Context: 运行上下文
// //	  _wg *sync.WaitGroup: 全局wait group
// //	  _isRecover bool: 是否是recover调用
// //
// // Deprecated
// func (bs *workingBurstService) _burstProcessor(index int, marginMode domain.MarginMode, ctx context.Context, wg *sync.WaitGroup, isRecover bool) {
// 	defer func() {
// 		if err := recover(); err != nil {
// 			logrus.Error(1, fmt.Sprintf("%s marginMod(%d) burstProcessor %d revocer error:", bs.contractCode, marginMode, index))
// 			logrus.Error(1, string(debug.Stack()))
// 			time.Sleep(time.Second)
// 			bs._burstProcessor(index, marginMode, ctx, wg, true)
// 		}
// 	}()
// 	if !isRecover {
// 		wg.Add(1)
// 	}

// 	workingIndex := fmt.Sprintf("%d", index)
// 	processorCtx := context.WithValue(ctx, "workingIndex", workingIndex)

// 	time.Sleep(time.Second * 2)
// 	logrus.Info(0, bs.contractCode, "burst processor", workingIndex, "started")
// 	defer logrus.Info(0, bs.contractCode, "burst processor", workingIndex, "stopped")

// Loop:
// 	for {
// 		select {
// 		case <-processorCtx.Done():
// 			break Loop

// 		default:

// 			// jsonStr := `{"burst_id":"862122581971435520","pos_id":"861705621085356032","pos_side":2,"user_id":"********","user_type":1,"account_type":"swap","base":"ETH","quote":"USDT","currency":"USDT","margin_balance":"95.********","total_margin":"11.9604","coin_pair_setting":{"optType":0,"base":"ETH","quote":"USDT","showRank":8,"leverMultiple":100,"defaultLeverage":20,"initPrice":"10000","basePrecision":3,"quotePrecision":5,"limitMarketAmount":"10","riskRate":"0.1","status":1,"orderLimitRate":"1","buyMinPricePercent":"0.1","buyMaxPricePercent":"0.1","sellMinPricePercent":"0.1","sellMaxPricePercent":"0.1","interest":"0","limitsA":"-0.00125","limitsB":"0.00125","marginRateGear":[{"gear":1,"start":"0","end":"-1","leverMultiple":100,"initRate":"0.05","warnRate":"0.01","maintenanceRate":"0.008"}],"forceLiquidationTime":**********},"marginMode":1,"burst_price":"1826.79","mark_price":"1826.79","collapse_price":"2780.71","collapse_price_formula":"分子: 账户余额\u003c98.********\u003e - 逐仓仓位保证金\u003c0\u003e + 其他全仓合约未实现盈亏\u003c0\u003e - 其他全仓合约维持保证金\u003c0\u003e - (多仓开仓均价\u003c0\u003e * 多仓仓位\u003c0\u003e) + (空仓开仓均价\u003c1794.06\u003e * 空仓仓位\u003c0.1\u003e) = \u003c278.18241136\u003e\n分母: 空仓仓位\u003c0.1\u003e - 多仓仓位\u003c0\u003e + (多仓仓位\u003c0\u003e * Taker费率\u003c0.0004\u003e) + (空仓仓位\u003c0.1\u003e * Taker费率\u003c0.0004\u003e) = \u003c0.10004\u003e\n破产价格: 278.18241136 / 0.10004 = 2780.71","long_pos_price":"0","short_pos_price":"1794.06","long_pos_amount":"0","short_pos_amount":"0.1","long_pos_value":"0","short_pos_value":"182.679","long_pos_un_real":"0","short_pos_un_real":"-3.273","frozen_margin":"0","pos_margin":"0","pnl":"-0.0095264780408477","frozen_pos":"0","leverage":15,"liquidation_price":"2759.74614","liquidation_type":1,"open_time":1680249517,"burst_level":1,"burst_time":1680348929006269340,"is_burst_to_zero":true,"user_level_rate":{"uid":"********","vip":0,"levelS":0,"userType":1,"bbMaker":"0.001","bbTaker":"0.001","contractMaker":"0.0002","contractTaker":"0.0004","isRobotFee":0,"LastRequestTime":1680348929}}`
// 			jsonStr, err := bs._getBurstingTask(marginMode, workingIndex)
// 			if err != nil {
// 				continue Loop
// 			}

// 			if len(jsonStr) < 1 {
// 				continue Loop
// 			}

// 			// 验证爆仓数据是否可用
// 			burstInfo, lockBurstId, err := bs._verifyBurstTask(workingIndex, jsonStr)
// 			if err != nil {
// 				logrus.Error(0, bs.contractCode, "burstProcessor verifyBurstTask error:", err, "marginMode", marginMode, "workingIndex", workingIndex)
// 				continue Loop
// 			}

// 			if burstInfo == nil {
// 				logrus.Error(0, bs.contractCode, "burstProcessor verifyBurstTask failed:", err, "marginMode", marginMode, "workingIndex", workingIndex)
// 				continue Loop
// 			}

// 			// 检查是否重复爆同一个仓
// 			bursting.Lock()
// 			// 生成工作中标记
// 			flag := bs.genWorkingFlag(burstInfo.UID, burstInfo.PosId)
// 			// 获取工作中索引
// 			if cacheWorkingIndex, ok := bursting.WorkingMap[flag]; !ok {
// 				// 保存恢复爆仓队列
// 				bs._saveRecoverTaskList(burstInfo.UID, burstInfo.BurstId, marginMode, jsonStr)
// 				bursting.WorkingMap[flag] = workingIndex
// 			} else {
// 				// 如果读取到的工作中索引!=当前线程索引
// 				if cacheWorkingIndex != workingIndex {
// 					logrus.Error(0, bs.contractCode, "marginMode(", marginMode, ") processor", workingIndex, "!=", "memory workingIndex:", workingIndex, "origin data:", jsonStr)
// 					// 暂时先忽略
// 					bursting.Unlock()
// 					runtime.Gosched()
// 					time.Sleep(time.Millisecond * 100)
// 					continue Loop
// 				}
// 			}
// 			bursting.Unlock()

// 			logrus.Info(0, bs.contractCode, burstInfo.UID, "lockBurstId", lockBurstId, burstInfo.BurstId)
// 			// 处理爆仓
// 			burstCtx := context.WithValue(processorCtx, "burstId", burstInfo.BurstId)
// 			bs._burstTradeWork(burstCtx, workingIndex, burstInfo)
// 		}

// 		runtime.Gosched()
// 		time.Sleep(time.Second)

// 	}

// 	wg.Done()
// }

// // 处理常规完成爆仓
// // Deprecated
// func (bs *workingBurstService) _checkBurstFinish(assetInfo *repository.AssetSwap, crossPos []repository.PosSwap, triggerBurstInfo *burstPosInfo) {
// 	if triggerBurstInfo.IsBurstToZero {
// 		switch triggerBurstInfo.MarginMode {
// 		case domain.MarginModeCross: // 全爆仓解锁处理
// 			if len(crossPos) < 1 {
// 				logrus.Info(0, "burstTradeWork Unlock Cross", triggerBurstInfo.UID)
// 				sendUnlockTask(bs.contractCode, triggerBurstInfo.UID, triggerBurstInfo.BurstId, triggerBurstInfo.PosId, triggerBurstInfo.BurstTime, domain.MarginModeCross, -1, false)
// 				// 清楚恢复列表爆仓信息
// 				bs._deleteRecoverTaskList(triggerBurstInfo.UID, triggerBurstInfo.BurstId, triggerBurstInfo.MarginMode)
// 				// 清除工作中标记
// 				bs._cleanWorkingFlag(triggerBurstInfo.UID, triggerBurstInfo.PosId)
// 				return
// 			} else {
// 				crossTotalPos := decimal.Zero
// 				for _, crossData := range crossPos {
// 					crossTotalPos = crossTotalPos.Add(crossData.Pos.Abs())
// 				}
// 				if crossTotalPos.Truncate(triggerBurstInfo.CoinPairSetting.AmountPrecision).LessThanOrEqual(decimal.Zero) {
// 					logrus.Info(0, "burstTradeWork Unlock Cross pos truncate zero", triggerBurstInfo.UID)
// 					sendUnlockTask(bs.contractCode, triggerBurstInfo.UID, triggerBurstInfo.BurstId, triggerBurstInfo.PosId, triggerBurstInfo.BurstTime, domain.MarginModeCross, -1, false)
// 					// 清楚恢复列表爆仓信息
// 					bs._deleteRecoverTaskList(triggerBurstInfo.UID, triggerBurstInfo.BurstId, triggerBurstInfo.MarginMode)
// 					// 清除工作中标记
// 					bs._cleanWorkingFlag(triggerBurstInfo.UID, triggerBurstInfo.PosId)
// 					return
// 				}
// 			}

// 		case domain.MarginModeIsolated: // 逐仓爆仓解锁
// 			switch triggerBurstInfo.PosSide {
// 			case domain.LongPos:
// 				if assetInfo.LongPos.Pos.Truncate(triggerBurstInfo.CoinPairSetting.AmountPrecision).LessThanOrEqual(decimal.Zero) {
// 					logrus.Info(0, "burstProcessor Unlock Isolated", domain.LongPos, triggerBurstInfo.UID)
// 					sendUnlockTask(bs.contractCode, triggerBurstInfo.UID, triggerBurstInfo.BurstId, triggerBurstInfo.PosId, triggerBurstInfo.BurstTime, domain.MarginModeIsolated, domain.LongPos, triggerBurstInfo.IsTrialPos)
// 					// 清楚恢复列表爆仓信息
// 					bs._deleteRecoverTaskList(triggerBurstInfo.UID, triggerBurstInfo.BurstId, triggerBurstInfo.MarginMode)
// 					// 清除工作中标记
// 					bs._cleanWorkingFlag(triggerBurstInfo.UID, triggerBurstInfo.PosId)
// 					return
// 				}

// 			case domain.ShortPos:
// 				if assetInfo.ShortPos.Pos.Truncate(triggerBurstInfo.CoinPairSetting.AmountPrecision).LessThanOrEqual(decimal.Zero) {
// 					logrus.Info(0, "burstProcessor Unlock Isolated", domain.ShortPos, triggerBurstInfo.UID)
// 					sendUnlockTask(bs.contractCode, triggerBurstInfo.UID, triggerBurstInfo.BurstId, triggerBurstInfo.PosId, triggerBurstInfo.BurstTime, domain.MarginModeIsolated, domain.ShortPos, triggerBurstInfo.IsTrialPos)
// 					// 清楚恢复列表爆仓信息
// 					bs._deleteRecoverTaskList(triggerBurstInfo.UID, triggerBurstInfo.BurstId, triggerBurstInfo.MarginMode)
// 					// 清除工作中标记
// 					bs._cleanWorkingFlag(triggerBurstInfo.UID, triggerBurstInfo.PosId)
// 					return
// 				}

// 			case domain.BothPos:
// 				if assetInfo.BothPos.Pos.Abs().Truncate(triggerBurstInfo.CoinPairSetting.AmountPrecision).LessThanOrEqual(decimal.Zero) {
// 					logrus.Info(0, "burstProcessor Unlock Isolated", domain.BothPos, triggerBurstInfo.UID)
// 					sendUnlockTask(bs.contractCode, triggerBurstInfo.UID, triggerBurstInfo.BurstId, triggerBurstInfo.PosId, triggerBurstInfo.BurstTime, domain.MarginModeIsolated, domain.BothPos, triggerBurstInfo.IsTrialPos)
// 					// 清楚恢复列表爆仓信息
// 					bs._deleteRecoverTaskList(triggerBurstInfo.UID, triggerBurstInfo.BurstId, triggerBurstInfo.MarginMode)
// 					// 清除工作中标记
// 					bs._cleanWorkingFlag(triggerBurstInfo.UID, triggerBurstInfo.PosId)
// 					return
// 				}

// 			default:

// 			}

// 		default:

// 		}
// 	}
// }

// // Deprecated
// func (bs *workingBurstService) _burstTradeWork(ctx context.Context, workingIndex string, triggerBurstInfo *burstPosInfo) {
// 	if ctx.Err() != nil {
// 		return
// 	}
// 	// 获取用户资产信息
// 	userCache, assetInfo, err := swapcache.GetUserCacheData(triggerBurstInfo.Base, triggerBurstInfo.Quote, triggerBurstInfo.UID)
// 	if err != nil {
// 		logrus.Error(0, triggerBurstInfo.ContractCode(), triggerBurstInfo.UID, "burstService burstProcessor GetUserCacheData error:", err)
// 		// 清除工作中标记
// 		bs._relineBurstTask(triggerBurstInfo, workingIndex)
// 		bs._cleanWorkingFlag(triggerBurstInfo.UID, triggerBurstInfo.PosId)
// 		return
// 	}

// 	// 获取用户仓位
// 	crossPos := make([]repository.PosSwap, 0)
// 	pCache := price.New()
// 	if assetInfo.AssetMode == futuresassetpb.AssetMode_ASSET_MODE_SINGLE {
// 		crossPos, _, err = userCache.UserPos(false, bs.quote, pCache)
// 	} else {
// 		crossPos, _, err = userCache.UserPos(true, "", pCache)
// 	}
// 	if err != nil {
// 		logrus.Error(0, bs.contractCode, "checkUserBurst userCache.UserPos error:", err, triggerBurstInfo.UID)
// 		return
// 	}

// 	bs._checkBurstFinish(assetInfo, crossPos, triggerBurstInfo)

// 	var originPosInfo repository.PosSwap
// 	switch triggerBurstInfo.MarginMode {
// 	case domain.MarginModeCross:
// 		for _, info := range crossPos {
// 			if info.ContractCode == triggerBurstInfo.ContractCode() {
// 				originPosInfo = info
// 				break
// 			}
// 		}

// 	case domain.MarginModeIsolated:
// 		switch triggerBurstInfo.PosSide {
// 		case domain.LongPos:
// 			originPosInfo = assetInfo.LongPos

// 		case domain.ShortPos:
// 			originPosInfo = assetInfo.ShortPos

// 		case domain.BothPos:
// 			originPosInfo = assetInfo.BothPos

// 		default:

// 		}

// 	default:

// 	}

// 	// 没匹配到仓位
// 	if len(originPosInfo.PosId) < 1 || originPosInfo.Pos.IsZero() {
// 		logrus.Error(0, triggerBurstInfo.ContractCode(), triggerBurstInfo.UID, "burst not fetch origin pos info:", triggerBurstInfo.PosId,
// 			"PosSide", triggerBurstInfo.PosSide, "LongPosAmount", triggerBurstInfo.LongPosAmount, "ShortPosAmount", triggerBurstInfo.ShortPosAmount)
// 		bs._relineBurstTask(triggerBurstInfo, workingIndex)
// 		return
// 	}

// 	var levelFilter repository.LevelFilter
// 	for _, configSetting := range triggerBurstInfo.CoinPairSetting.MarginRateGear {
// 		if configSetting.Gear == triggerBurstInfo.BurstLevel {
// 			levelFilter = repository.LevelFilter{
// 				Level:             configSetting.Gear,
// 				Lever:             configSetting.LeverMultiple,
// 				HighLimit:         configSetting.End,
// 				LowLimit:          configSetting.Start,
// 				InitMarginRate:    configSetting.InitRate,
// 				WarnMarginRate:    configSetting.WarnRate,
// 				HoldingMarginRate: configSetting.MaintenanceRate,
// 			}
// 		}
// 	}

// 	// 获取user费率
// 	userLevelRateInfo, err := GetContractUserLevelRate(assetInfo.UID)
// 	if err != nil {
// 		logrus.Error(0, bs.contractCode, assetInfo.UID, "crossBurstTrigger GetContractUserLevelRate error:", err)
// 		// 获取默认setting等级
// 		if levelInfo, ok := DefaultUserLevelRate.Map[0]; ok {
// 			logrus.Info(0, bs.contractCode, assetInfo.UID, "crossBurstTrigger use default taker rate")
// 			userLevelRateInfo.UID = assetInfo.UID
// 			userLevelRateInfo.ContractTaker = levelInfo.ContractTaker
// 		} else {
// 			logrus.Error(0, bs.contractCode, "crossBurstTrigger no level 0 taker rate:", err)
// 			bs._relineBurstTask(triggerBurstInfo, workingIndex)
// 			return
// 		}
// 	}

// 	workingBurstInfo := new(burstPosInfo)
// 	workingBurstInfo._updatePos(triggerBurstInfo.BurstId, assetInfo, userCache, triggerBurstInfo.CoinPairSetting, originPosInfo,
// 		crossPos, levelFilter, triggerBurstInfo.BurstTime, pCache, userLevelRateInfo)
// 	workingBurstInfo.IsBurstToZero = triggerBurstInfo.IsBurstToZero
// 	workingBurstInfo.LiquidationType = triggerBurstInfo.LiquidationType

// 	// 撤单类型
// 	cancelType := domain.CancelTypeBurst
// 	switch workingBurstInfo.LiquidationType {
// 	case domain.LiquidationTypeReduce:
// 		cancelType = domain.CancelTypeReduce

// 	default:

// 	}

// 	// 撤销平仓单
// 	if workingBurstInfo.FrozenPos.GreaterThan(decimal.Zero) {
// 		if workingBurstInfo.UID == "25974182" {
// 			logrus.Info(0, "======================= workingBurstInfo.FrozenPos", workingBurstInfo.FrozenPos)
// 			os.Exit(1)
// 		}
// 		success, _ := match.Service.ConditionCancel(workingBurstInfo.UID, workingBurstInfo.Base, workingBurstInfo.Quote, domain.Close, 0, int32(workingBurstInfo.MarginMode), int32(cancelType), 0)
// 		if !success {
// 			logrus.Error(0, bs.contractCode, workingBurstInfo.UID, "burstTradeWork ConditionCancel Close failed")
// 		}
// 		time.Sleep(300 * time.Millisecond)
// 		bs._burstTradeWork(ctx, workingIndex, triggerBurstInfo)
// 		return
// 	}

// 	// 全仓
// 	// 撤销开仓单
// 	if workingBurstInfo.FrozenMargin.GreaterThan(decimal.Zero) {
// 		logrus.Error(0, bs.contractCode, workingBurstInfo.UID, "burstTradeWork ConditionCancel cross Open")
// 		success, _ := match.Service.ConditionCancel(workingBurstInfo.UID, workingBurstInfo.Base, workingBurstInfo.Quote, domain.Open, 0, int32(domain.MarginModeCross), int32(cancelType), 0)
// 		if !success {
// 		}
// 		time.Sleep(300 * time.Millisecond)
// 		if workingBurstInfo.PosSide != domain.BothPos {
// 			bs._burstTradeWork(ctx, workingIndex, triggerBurstInfo)
// 			return
// 		}
// 	}

// 	// 可以自己多空抵消
// 	if workingBurstInfo.PosSide != domain.BothPos && workingBurstInfo.MarginMode == domain.MarginModeCross &&
// 		workingBurstInfo.LongPosAmount.GreaterThan(decimal.Zero) && workingBurstInfo.ShortPosAmount.GreaterThan(decimal.Zero) {
// 		logrus.Info(0, workingBurstInfo.ContractCode(), workingBurstInfo.UID, "_burstPos.LongPosAmount", workingBurstInfo.LongPosAmount, "_burstPos.ShortPosAmount", workingBurstInfo.ShortPosAmount)
// 		err = bs.SelfForceBurst(workingBurstInfo, assetInfo, decimal.Zero, domain.LiquidationTypeBurstSelfReduce)
// 		if err != nil {
// 			logrus.Error(0, workingBurstInfo.ContractCode(), workingBurstInfo.UID, "burstTradeWork SelfForceBurst error:", err)
// 		}
// 		time.Sleep(300 * time.Millisecond)
// 		bs._burstTradeWork(ctx, workingIndex, triggerBurstInfo)
// 		return
// 	}
// 	// }

// 	switch workingBurstInfo.LiquidationType {
// 	// 减仓
// 	case domain.LiquidationTypeReduce:
// 		// 强制减仓（降级）
// 		forceDownOk, isSelfTrade, reduceAmount, err := bs.ForceDownPosLevel(workingBurstInfo, assetInfo, pCache)
// 		if err != nil {
// 			time.Sleep(time.Second)
// 			logrus.Error(0, bs.contractCode, workingBurstInfo.UID, "force done level failed", err)
// 			bs._relineBurstTask(triggerBurstInfo, workingIndex)
// 			return
// 		}
// 		logrus.Info(0, workingBurstInfo.ContractCode(), workingBurstInfo.UID, "not last level, forceDownOk", forceDownOk, "isSelfTrade", isSelfTrade, "reduceAmount", reduceAmount)
// 		if forceDownOk {
// 			// 不是自己抵消时检查是否成交
// 			if !isSelfTrade && reduceAmount.GreaterThan(decimal.Zero) {
// 				time.Sleep(time.Second * 3)
// 				// 高等级降级不存在穿仓，所以跳过风险准备金检查  _skipRiskMargin:true
// 				bs.CheckDeal(workingBurstInfo, true, triggerBurstInfo.BurstLevel, domain.CancelTypeReduce, domain.LiquidationTypeReduce, reduceAmount)
// 			}

// 			// 自解锁（只用于减仓结束）
// 			// 更新爆仓状态
// 			burstEvent := monitor.BurstEvent{
// 				UID:          workingBurstInfo.UID,
// 				MarginMode:   workingBurstInfo.MarginMode,
// 				ContractCode: workingBurstInfo.ContractCode(),
// 				BurstId:      workingBurstInfo.BurstId,
// 				BurstTime:    workingBurstInfo.BurstTime,
// 			}
// 			unlockParams := repository.BurstLockParam{
// 				ContractCode: workingBurstInfo.ContractCode(),
// 				Liquidation: cache.LiquidationInfo{
// 					UID:        workingBurstInfo.UID,
// 					MarginMode: workingBurstInfo.MarginMode,
// 				},
// 			}
// 			if workingBurstInfo.MarginMode == domain.MarginModeIsolated {
// 				burstEvent.PosSide = workingBurstInfo.PosSide
// 				unlockParams.Liquidation.PosSide = workingBurstInfo.PosSide
// 			}
// 			jsonBytes, _ := json.Marshal(burstEvent)
// 			swapcache.SendSwapTask(workingBurstInfo.ContractCode(), cache.SwapBurstTask{
// 				Type:     cache.TaskTypeBurstEvent,
// 				Data:     jsonBytes,
// 				FuncName: "UpdateBurstStatus",
// 			})

// 			time.Sleep(time.Second)

// 			// 手动强制解锁
// 			err = cachelock.UnlockBurstUser(unlockParams, false)
// 			if err != nil {
// 				logrus.Error(0, bs.contractCode, workingBurstInfo.UID, "burstTradeWork UnlockBurstUser error:", err, "params:", fmt.Sprintf("%+v", unlockParams))
// 			}

// 			bs._cleanWorkingFlag(triggerBurstInfo.UID, triggerBurstInfo.PosId)
// 			return
// 		}

// 	// 爆仓
// 	default:
// 		logrus.Info(0, workingBurstInfo.UID, bs.contractCode, "short + long =", workingBurstInfo.ShortPosAmount.Add(workingBurstInfo.LongPosAmount))
// 		if workingBurstInfo.ShortPosAmount.Add(workingBurstInfo.LongPosAmount).GreaterThan(decimal.Zero) {
// 			fmt.Println("=== assetInfo.LongPos.Pos", assetInfo.LongPos.Pos)
// 			fmt.Println("=== assetInfo.ShortPos.Pos", assetInfo.ShortPos.Pos)
// 			fmt.Println("=== assetInfo.BothPos.Pos", assetInfo.BothPos.Pos)
// 			// 爆仓处理
// 			bs.DepthTradeBurst(workingBurstInfo, assetInfo)
// 		}
// 		time.Sleep(time.Second)

// 		// 清除工作中标记
// 		bs._relineBurstTask(triggerBurstInfo, workingIndex)
// 		bs._cleanWorkingFlag(triggerBurstInfo.UID, triggerBurstInfo.PosId)

// 	}
// 	return
// }

// // DepthTradeBurst 爆仓
// //
// //	Params:
// //
// //	  _burstPos *burstPos: 爆仓仓位信息
// //
// // Deprecated
// func (bs *workingBurstService) DepthTradeBurst(workingBurstInfo *burstPosInfo, assetInfo *repository.AssetSwap) {
// 	defer func() {
// 		if err := recover(); err != nil {
// 			logrus.Error(0, bs.contractCode, workingBurstInfo.UID, "DepthTradeBurst recover error:", err)
// 			logrus.Error(0, string(debug.Stack()))
// 		}
// 	}()

// 	checkDeal := false
// 	pricePrecision := int32(domain.PricePrecision)
// 	if bs.coinPairInfo.PricePrecision > 0 {
// 		pricePrecision = bs.coinPairInfo.PricePrecision
// 	}
// 	closeAmount := decimal.Zero
// 	closeSide := match.SideSell
// 	posSide := domain.Long
// 	posFlag := ""

// 	positionMode := domain.HoldModeHedge
// 	if workingBurstInfo.PosSide == domain.BothPos {
// 		positionMode = domain.HoldModeBoth
// 	}

// 	isMixMargin := match.NotMixMargin
// 	if assetInfo.AssetMode == domain.AssetMode {
// 		isMixMargin = match.IsMixMargin
// 	}

// 	// 平多
// 	if workingBurstInfo.LongPosAmount.Truncate(domain.CurrencyPrecision).GreaterThan(decimal.Zero) {
// 		checkDeal = true
// 		closeSide = match.SideSell
// 		posSide = domain.Long
// 		closeAmount = workingBurstInfo.LongPosAmount
// 		posFlag = "LongPos"
// 	}

// 	// 平空
// 	if workingBurstInfo.ShortPosAmount.Truncate(domain.CurrencyPrecision).GreaterThan(decimal.Zero) {
// 		checkDeal = true
// 		closeSide = match.SideBuy
// 		posSide = domain.Short
// 		closeAmount = workingBurstInfo.ShortPosAmount
// 		posFlag = "ShortPos"
// 	}

// 	logrus.Info(0, bs.contractCode, workingBurstInfo.UID, "DepthTradeBurst close amount:", closeAmount, "burst pos:", fmt.Sprintf("%+v", workingBurstInfo))

// 	// 平仓操作
// 	if closeAmount.GreaterThan(decimal.Zero) {
// 		if workingBurstInfo.CollapsePrice.LessThanOrEqual(decimal.Zero) {
// 			logrus.Info(0, "---------------------------- CollapsePrice LessThanOrEqual 0")
// 			closeResponse, err := match.Service.ClosePositions(&match.ClosePositionParams{
// 				PositionParams: match.PositionParams{
// 					Side:         closeSide,
// 					PosSide:      posSide,
// 					IsLimitOrder: match.IsLimitOrderYes,
// 					OrderType:    match.OrderTypeNormal,
// 					Amount:       closeAmount.Truncate(domain.CurrencyPrecision),
// 					TriggerPrice: decimal.Zero,
// 				},
// 				UID:             workingBurstInfo.UID,
// 				AccountType:     match.AccountTypeSwap,
// 				Base:            bs.base,
// 				Quote:           bs.quote,
// 				Leverage:        workingBurstInfo.Leverage,
// 				Platform:        match.PlatformSystem,
// 				LiquidationType: int(domain.LiquidationTypeBurst),
// 				MarginMode:      int(workingBurstInfo.MarginMode),
// 				TimeInForce:     match.TimeInForceTypeGTC,
// 				PositionMode:    positionMode,
// 				IsMixMargin:     isMixMargin,
// 				Depth:           5,
// 				BurstId:         workingBurstInfo.BurstId,
// 				BurstTime:       workingBurstInfo.BurstTime,
// 			})
// 			if err != nil {
// 				logrus.Error(0, workingBurstInfo.ContractCode(), workingBurstInfo.UID, "DepthTradeBurst depth 5 ClosePositions", posFlag, "close error:", err)
// 				checkDeal = false
// 			} else {
// 				logrus.Info(0, workingBurstInfo.ContractCode(), workingBurstInfo.UID, "ClosePositions Response:", fmt.Sprintf("%+v", closeResponse))
// 				if closeResponse.Code != 200 {
// 					logrus.Error(0, workingBurstInfo.ContractCode(), workingBurstInfo.UID, "DepthTradeBurst", posFlag, "CollapsePrice = 0 --> depth 5 close Response", fmt.Sprintf("%+v", closeResponse), "amount:", workingBurstInfo.LongPosAmount.Truncate(domain.CurrencyPrecision), err)

// 					err = bs._depth5Close(workingBurstInfo, -1, domain.CancelTypeBurst, domain.LiquidationTypeBurst, decimal.Zero)
// 					if err != nil {
// 						logrus.Error(0, workingBurstInfo.ContractCode(), workingBurstInfo.UID, "DepthTradeBurst depth5Close error:", err, "burstPosInfo:", fmt.Sprintf("%+v", workingBurstInfo))
// 						checkDeal = false
// 					}
// 				}
// 			}
// 		} else {
// 			logrus.Info(0, "---------------------------- CollapsePrice GreatThan 0", workingBurstInfo.CollapsePrice)
// 			workingBurstInfo.CollapsePrice.Mul(closeAmount)

// 			closeResponse, err := match.Service.ClosePositions(&match.ClosePositionParams{
// 				PositionParams: match.PositionParams{
// 					Side:         closeSide,
// 					PosSide:      posSide,
// 					IsLimitOrder: match.IsLimitOrderYes,
// 					OrderType:    match.OrderTypeNormal,
// 					Price:        workingBurstInfo.CollapsePrice.Truncate(pricePrecision),
// 					Amount:       closeAmount.Truncate(domain.CurrencyPrecision),
// 					TriggerPrice: decimal.Zero,
// 				},
// 				UID:             workingBurstInfo.UID,
// 				AccountType:     match.AccountTypeSwap,
// 				Base:            bs.base,
// 				Quote:           bs.quote,
// 				Leverage:        workingBurstInfo.Leverage,
// 				Platform:        match.PlatformSystem,
// 				LiquidationType: int(domain.LiquidationTypeBurst),
// 				MarginMode:      int(workingBurstInfo.MarginMode),
// 				TimeInForce:     match.TimeInForceTypeGTC,
// 				PositionMode:    positionMode,
// 				IsMixMargin:     isMixMargin,
// 				BurstId:         workingBurstInfo.BurstId,
// 				BurstTime:       workingBurstInfo.BurstTime,
// 			})
// 			if err != nil {
// 				logrus.Error(0, workingBurstInfo.ContractCode(), workingBurstInfo.UID, "DepthTradeBurst ClosePositions", posFlag, "close error:", err)
// 				checkDeal = false
// 			} else {
// 				logrus.Info(0, workingBurstInfo.ContractCode(), workingBurstInfo.UID, "ClosePositions Response:", fmt.Sprintf("%+v", closeResponse))
// 				if closeResponse.Code != 200 {
// 					logrus.Error(0, workingBurstInfo.ContractCode(), workingBurstInfo.UID, "DepthTradeBurst", posFlag, "close Response", fmt.Sprintf("%+v", closeResponse), "amount:", workingBurstInfo.LongPosAmount.Truncate(domain.CurrencyPrecision), err)
// 					// 获取用户资产信息
// 					err = bs._depth5Close(workingBurstInfo, -1, domain.CancelTypeBurst, domain.LiquidationTypeBurst, decimal.Zero)
// 					if err != nil {
// 						logrus.Error(0, workingBurstInfo.ContractCode(), workingBurstInfo.UID, "DepthTradeBurst depth5Close error:", err)
// 						checkDeal = false
// 					}
// 				}
// 			}
// 		}
// 	}

// 	// 检查成交
// 	if checkDeal {
// 		time.Sleep(time.Second * 3)
// 		bs.CheckDeal(workingBurstInfo, false, workingBurstInfo.BurstLevel, domain.CancelTypeBurst, domain.LiquidationTypeBurst, decimal.Zero)
// 	} else {
// 		return
// 	}
// 	return
// }

// // ForceRivalBurst 强制对手方平仓
// //
// //	Params:
// //	  assetInfo *repository.AssetSwap: 账户信息
// //	  burstPos *burstPos: 爆仓仓位信息
// //	  liquidationType domain.LiquidationType: 强平类型
// //	  reduceAmount decimal.Decimal: 强制强平数量, 如果给了就会覆盖掉爆仓信息的数量
// //
// // Deprecated
// func (bs *workingBurstService) ForceRivalBurst(assetInfo *repository.AssetSwap, workingBurstInfo *burstPosInfo, liquidationType domain.LiquidationType, reduceAmount decimal.Decimal) {
// 	// 更新爆仓查询强制减仓状态
// 	burstEvent := monitor.BurstEvent{
// 		UID:       workingBurstInfo.UID,
// 		BurstId:   workingBurstInfo.BurstId,
// 		BurstTime: time.Now().UnixNano(),
// 	}
// 	rivalSide := domain.ShortPos
// 	tradeSide := match.SideSell
// 	tradeAmount := workingBurstInfo.LongPosAmount
// 	rivalListKey := cachekey.GetBurstRivalShortRankRedisKey(bs.contractCode)

// 	switch workingBurstInfo.PosSide {
// 	case domain.ShortPos:
// 		rivalSide = domain.LongPos
// 		tradeSide = match.SideBuy
// 		tradeAmount = workingBurstInfo.ShortPosAmount
// 		rivalListKey = cachekey.GetBurstRivalLongRankRedisKey(bs.contractCode)

// 	case domain.BothPos:
// 		if workingBurstInfo.ShortPosAmount.GreaterThan(workingBurstInfo.LongPosAmount) {
// 			rivalSide = domain.LongPos
// 			tradeSide = match.SideBuy
// 			tradeAmount = workingBurstInfo.ShortPosAmount
// 			rivalListKey = cachekey.GetBurstRivalLongRankRedisKey(bs.contractCode)
// 		}

// 	default:

// 	}

// 	if reduceAmount.GreaterThan(decimal.Zero) {
// 		tradeAmount = reduceAmount
// 	}
// 	rivalUsers, err := redisCli.ZRevRangeByScoreWithScores(rivalListKey, 0, 500)
// 	if err != nil {
// 		logrus.Error(0, "ForceRivalBurst ZRevRangeByScoreWithScores error:", err)
// 		return
// 	}
// 	if len(rivalUsers) < 1 {
// 		logrus.Error(0, bs.contractCode, "ForceRivalBurst no rival user from", rivalListKey)
// 	}
// 	for _, userInfo := range rivalUsers {
// 		if rivalUserId, ok := userInfo.Member.(string); ok {
// 			userLiquidationType := liquidationType
// 			rivalUserLiquidationType := liquidationType
// 			switch liquidationType {
// 			case domain.LiquidationTypeBurst:
// 				fallthrough

// 			case domain.LiquidationTypeReduce:
// 				if workingBurstInfo.UID != rivalUserId {
// 					rivalUserLiquidationType = domain.LiquidationTypeStopProfitReduce
// 				}

// 			default:

// 			}

// 			rivalUserBurstInfo, rivalUserAssetInfo := bs._composeCurrentUserBurstPosByPosSide(workingBurstInfo.Base, workingBurstInfo.Quote, rivalUserId, workingBurstInfo.BurstId, rivalSide, workingBurstInfo.CoinPairSetting, false)
// 			if rivalUserBurstInfo == nil {
// 				logrus.Error(0, workingBurstInfo.ContractCode(), "ForceRivalBurst composeCurrentUserBurstPosByPosSide failed: BurstId", workingBurstInfo.BurstId, "rivalUserId", rivalUserId, "rivalSide", rivalSide)
// 				return
// 			}
// 			rivalUserBurstInfo.LiquidationType = rivalUserLiquidationType

// 			rivalAmount := decimal.Zero
// 			switch rivalSide {
// 			case domain.ShortPos:
// 				rivalAmount = rivalUserBurstInfo.ShortPosAmount

// 			case domain.LongPos:
// 				rivalAmount = rivalUserBurstInfo.LongPosAmount

// 			default:

// 			}
// 			pricePrecision := int32(domain.PricePrecision)
// 			if bs.coinPairInfo.PricePrecision > 0 {
// 				pricePrecision = bs.coinPairInfo.PricePrecision
// 			}
// 			lastPrice, err := sharedcache.GetContractLastPrice(bs.base, bs.quote)
// 			if err != nil {
// 				logrus.Error(0, "ForceRivalBurst GetContractLastPrice error:", err)
// 				return
// 			}
// 			reducePosParam := &match.ReducePositionsParams{
// 				UserSide: tradeSide,

// 				UID:                 workingBurstInfo.UID,
// 				UserLeverage:        workingBurstInfo.Leverage,
// 				UserMarginMode:      int(workingBurstInfo.MarginMode),
// 				UserLiquidationType: int(userLiquidationType),
// 				UserHoldMode:        assetInfo.PositionMode,
// 				UserIsMixMargin:     assetInfo.AssetMode,
// 				UserTimeInForce:     match.TimeInForceTypeGTC,

// 				TargetUserId:              rivalUserId,
// 				TargetUserLeverage:        rivalUserBurstInfo.Leverage,
// 				TargetUserMarginMode:      int(rivalUserBurstInfo.MarginMode),
// 				TargetUserLiquidationType: int(rivalUserLiquidationType),
// 				TargetUserHoldMode:        rivalUserAssetInfo.PositionMode,
// 				TargetUserIsMixMargin:     rivalUserAssetInfo.AssetMode,
// 				TargetUserTimeInForce:     match.TimeInForceTypeGTC,

// 				AccountType: match.AccountTypeSwap,
// 				Platform:    match.PlatformSystem,
// 				Base:        workingBurstInfo.Base,
// 				Quote:       workingBurstInfo.Quote,
// 				Price:       lastPrice.Truncate(pricePrecision),
// 				BurstId:     workingBurstInfo.BurstId,
// 				BurstTime:   workingBurstInfo.BurstTime,
// 			}
// 			if rivalAmount.IsZero() {
// 				continue
// 			}
// 			reducePosParam.UserMarginMode = int(workingBurstInfo.MarginMode)
// 			reducePosParam.TargetUserMarginMode = int(rivalUserBurstInfo.MarginMode)
// 			retryTimes := 0
// 		retryReduce:
// 			if rivalAmount.GreaterThanOrEqual(tradeAmount) {
// 				reducePosParam.Amount = tradeAmount.Truncate(domain.CurrencyPrecision)
// 				res, err := match.Service.BothReducePositions(reducePosParam)
// 				if err != nil {
// 					logrus.Error(0, "rivalAmount > 0 ForceRivalBurst BothReducePositions error:", err)
// 				}
// 				logrus.Info(0, "rivalAmount.GreaterThanOrEqual(tradeAmount)", fmt.Sprintf("%+v", res))
// 				if res.Code == 200 {
// 					// 发送减仓盈利方通知
// 					go util.SendMailAndSmsToNoticeBurst(rivalUserId, bs.contractCode, domain.MSForceReduceTarget, "", decimal.Zero)
// 					// 更新爆仓记录  是否对手方减仓 和 对手方减仓数量
// 					burstEvent.ForceRivalAmount = tradeAmount.Truncate(domain.CurrencyPrecision)
// 					jsonBytes, _ := json.Marshal(burstEvent)
// 					swapcache.SendSwapTask(bs.contractCode, cache.SwapBurstTask{
// 						Type:     cache.TaskTypeBurstEvent,
// 						Data:     jsonBytes,
// 						FuncName: "UpdateBurstIsForceRival",
// 					})
// 					return
// 				} else {
// 					logrus.Error(0, workingBurstInfo.UID, fmt.Sprintf("rivalAmount > 0 ForceRivalBurst BothReducePositions: %+v %+v", res, reducePosParam), err)
// 				}
// 				if res.Code == 251119 && retryTimes <= 5 {
// 					time.Sleep(time.Second)
// 					retryTimes += 1
// 					goto retryReduce
// 				}
// 			} else {
// 				reducePosParam.Amount = rivalAmount
// 				res, err := match.Service.BothReducePositions(reducePosParam)
// 				if err != nil {
// 					logrus.Error(0, "ForceRivalBurst BothReducePositions error:", err)
// 				}
// 				logrus.Info(0, "rivalAmount.LessThan(tradeAmount)", fmt.Sprintf("%+v", res))
// 				if res.Code == 200 {
// 					// 发送减仓盈利方通知
// 					go util.SendMailAndSmsToNoticeBurst(rivalUserId, bs.contractCode, domain.MSForceReduceTarget, "", decimal.Zero)
// 					// 更新爆仓记录  是否对手方减仓 和 对手方减仓数量
// 					burstEvent.ForceRivalAmount = rivalAmount.Truncate(domain.CurrencyPrecision)
// 					jsonBytes, _ := json.Marshal(burstEvent)
// 					swapcache.SendSwapTask(bs.contractCode, cache.SwapBurstTask{
// 						Type:     cache.TaskTypeBurstEvent,
// 						Data:     jsonBytes,
// 						FuncName: "UpdateBurstIsForceRival",
// 					})

// 					tradeAmount = tradeAmount.Sub(rivalAmount)
// 				} else {
// 					logrus.Error(0, workingBurstInfo.UID, fmt.Sprintf("ForceRivalBurst BothReducePositions: %+v %+v", res, reducePosParam), err)
// 				}
// 				if res.Code == 251119 && retryTimes <= 5 {
// 					time.Sleep(time.Second)
// 					retryTimes += 1
// 					goto retryReduce
// 				}
// 			}
// 		}
// 	}

// 	return
// }

// // _depth5Close 第五档价格下平仓委托
// //
// //	Params:
// //	  _userCache *swapcache.PosCache: 用户缓存信息
// //	  _assetInfo *repository.AssetSwap: 用户资产信息
// //	  _crossPos []repository.PosSwap: 全仓合约仓位列表
// //	  _isolatedPos []repository.PosSwap: 逐仓合约仓位列表
// //	  _burstPos *burstPos: 爆仓仓位信息
// //	  _originBurstLevel int: 触发爆仓等级
// //	  _cancelType domain.CancelType: 委托撤销类型
// //	  _liquidationType domain.LiquidationType: 强平类型
// //	  _reduceAmount decimal.Decimal: 减仓数量
// //
// // Deprecated
// func (bs *workingBurstService) _depth5Close(workingBurstInfo *burstPosInfo, originBurstLevel int, cancelType domain.CancelType, liquidationType domain.LiquidationType, reduceAmount decimal.Decimal) error {
// 	// 获取用户资产信息
// 	_, assetInfo, err := swapcache.GetUserCacheData(workingBurstInfo.Base, workingBurstInfo.Quote, workingBurstInfo.UID)
// 	if err != nil {
// 		logrus.Error(0, bs.contractCode, workingBurstInfo.UID, "depth5Close GetUserCacheData error:", err, "burstPosInfo:", fmt.Sprintf("%+v", workingBurstInfo))
// 		time.Sleep(time.Millisecond * 200)
// 		return err
// 	}

// 	lastPrice, err := match.Service.GetContractLastPrice(workingBurstInfo.Base, workingBurstInfo.Quote)
// 	if err != nil {
// 		logrus.Error(0, bs.contractCode, workingBurstInfo.UID, "depth5Close get match last price error:", err)
// 		time.Sleep(time.Millisecond * 200)
// 		return err
// 	}
// 	if lastPrice.IsZero() {
// 		logrus.Error(0, bs.contractCode, workingBurstInfo.UID, "depth5Close last price is zero", err)
// 		time.Sleep(time.Millisecond * 200)
// 		return err
// 	}
// 	closeAmount := decimal.Zero
// 	closeOrderSide := domain.Sell
// 	closePosSide := domain.Long
// 	closePrice := decimal.Zero
// 	sellPrice, buyPrice, err := match.Service.GetDepthPrice(&match.DepthPriceParams{
// 		AccountType: match.AccountTypeSwap,
// 		Base:        bs.base,
// 		Quote:       bs.quote,
// 		Depth:       5,
// 	})
// 	if err != nil {
// 		logrus.Error(0, bs.contractCode, "depth5Close get depth price error:", err)
// 		time.Sleep(time.Millisecond * 200)
// 		return err
// 	}

// 	positionMode := domain.HoldModeHedge
// 	if workingBurstInfo.PosSide == domain.BothPos {
// 		positionMode = domain.HoldModeBoth
// 	}

// 	isMixMargin := match.NotMixMargin
// 	if assetInfo.AssetMode == domain.AssetMode {
// 		isMixMargin = match.IsMixMargin
// 	}

// 	if workingBurstInfo.ShortPosAmount.GreaterThan(decimal.Zero) {
// 		closeOrderSide = domain.Buy
// 		closePosSide = domain.Short
// 	}

// 	// 更新仓位信息
// 	// _burstPos.updatePos(_assetInfo, _userCache, _crossPos, _isolatedPos, posSide)
// 	if originBurstLevel > -1 && workingBurstInfo.BurstLevel != originBurstLevel {
// 		logrus.Error(0, bs.contractCode, workingBurstInfo.UID, "depth5Close burstPosInfo.BurstLevel(", workingBurstInfo.BurstLevel, ") != originBurstLevel(", originBurstLevel, ")")
// 		return err
// 	}

// 	// 确定平仓数量
// 	if liquidationType == domain.LiquidationTypeReduce {
// 		switch workingBurstInfo.PosSide {
// 		case domain.LongPos:
// 			closeAmount = assetInfo.LongPos.Pos.Sub(assetInfo.LongPos.PosAvailable)

// 		case domain.ShortPos:
// 			closeAmount = assetInfo.ShortPos.Pos.Sub(assetInfo.ShortPos.PosAvailable)

// 		case domain.BothPos:
// 			closeAmount = assetInfo.BothPos.Pos.Abs().Sub(assetInfo.BothPos.PosAvailable)

// 		default:

// 		}
// 	} else {
// 		switch closeOrderSide {
// 		case domain.Buy:
// 			closeAmount = workingBurstInfo.ShortPosAmount

// 		case domain.Sell:
// 			closeAmount = workingBurstInfo.LongPosAmount

// 		default:

// 		}
// 	}

// 	if reduceAmount.GreaterThan(decimal.Zero) {
// 		closeAmount = reduceAmount
// 	}

// 	// 空仓可平
// 	if workingBurstInfo.ShortPosAmount.GreaterThan(decimal.Zero) {
// 		closePrice = sellPrice
// 		if closePrice.Div(lastPrice).LessThanOrEqual(decimal.NewFromFloat(1.05)) &&
// 			closePrice.Div(lastPrice).GreaterThanOrEqual(decimal.NewFromFloat(0.95)) {
// 			success, _ := match.Service.ConditionCancel(workingBurstInfo.UID, strings.ToLower(bs.base), strings.ToLower(bs.quote), domain.Close, domain.Buy, int32(workingBurstInfo.MarginMode), int32(cancelType), 0)
// 			if !success {
// 			}
// 			time.Sleep(time.Second)
// 		} else {
// 			errStr := fmt.Sprintln(bs.contractCode, "depth5Close sellPrice(", sellPrice, ") overflow 5% lastPrice(", lastPrice, ") error")
// 			logrus.Error(0, errStr)
// 			time.Sleep(time.Millisecond * 200)
// 			bs.ForceRivalBurst(assetInfo, workingBurstInfo, liquidationType, closeAmount)
// 			time.Sleep(time.Millisecond * 200)
// 			return nil
// 		}
// 	}
// 	// 多仓可平
// 	if workingBurstInfo.LongPosAmount.GreaterThan(decimal.Zero) {
// 		closePrice = buyPrice
// 		if closePrice.Div(lastPrice).LessThanOrEqual(decimal.NewFromFloat(1.05)) &&
// 			closePrice.Div(lastPrice).GreaterThanOrEqual(decimal.NewFromFloat(0.95)) {
// 			success, _ := match.Service.ConditionCancel(workingBurstInfo.UID, strings.ToLower(bs.base), strings.ToLower(bs.quote), domain.Close, domain.Sell, int32(workingBurstInfo.MarginMode), int32(cancelType), 0)
// 			if !success {
// 			}
// 			time.Sleep(time.Second)
// 		} else {
// 			errStr := fmt.Sprintln(bs.contractCode, "depth5Close buyPrice(", buyPrice, ") overflow 5% lastPrice(", lastPrice, ") error")
// 			logrus.Error(0, errStr)
// 			time.Sleep(time.Millisecond * 200)
// 			bs.ForceRivalBurst(assetInfo, workingBurstInfo, liquidationType, closeAmount)
// 			time.Sleep(time.Millisecond * 200)
// 			return nil
// 		}
// 	}
// 	if closeAmount.GreaterThan(decimal.Zero) && closePrice.GreaterThan(decimal.Zero) {
// 		closeResponse, err := match.Service.ClosePositions(&match.ClosePositionParams{
// 			PositionParams: match.PositionParams{
// 				Side:         closeOrderSide,
// 				IsLimitOrder: match.IsLimitOrderYes,
// 				PosSide:      closePosSide,
// 				OrderType:    match.OrderTypeNormal,
// 				Price:        closePrice,
// 				Amount:       closeAmount.Truncate(domain.CurrencyPrecision),
// 				TriggerPrice: decimal.Zero,
// 			},
// 			UID:             workingBurstInfo.UID,
// 			AccountType:     match.AccountTypeSwap,
// 			Base:            bs.base,
// 			Quote:           bs.quote,
// 			Leverage:        workingBurstInfo.Leverage,
// 			Platform:        match.PlatformSystem,
// 			LiquidationType: int(liquidationType),
// 			MarginMode:      int(workingBurstInfo.MarginMode),
// 			TimeInForce:     match.TimeInForceTypeGTC,
// 			PositionMode:    positionMode,
// 			IsMixMargin:     isMixMargin,
// 			BurstId:         workingBurstInfo.BurstId,
// 			BurstTime:       workingBurstInfo.BurstTime,
// 		})
// 		if err != nil {
// 			logrus.Error(0, "depth5Close ClosePositions error:", err)
// 			return err
// 		}
// 		if closeResponse.Code != 200 {
// 			closeFlag := "long"
// 			if closeOrderSide == match.SideBuy {
// 				closeFlag = "short"
// 			}
// 			logrus.Error(0, bs.contractCode, "depth5Close ClosePositions", closeFlag, "pos fail:", fmt.Sprintf("%+v", closeResponse))
// 			// 平仓数量超过深度限制
// 			if closeResponse.Code == 300025 {
// 				bs.ForceRivalBurst(assetInfo, workingBurstInfo, liquidationType, closeAmount)
// 			}
// 			runtime.Gosched()
// 		}
// 	}
// 	return nil
// }

// // CheckDeal 检查强平订单成交
// //
// //	Params:
// //	  _burstPos *burstPos: 爆仓仓位信息
// //	  _skipRiskMargin bool: 是否跳过风险准备金检查
// //	  _originBurstLevel int: 触发爆仓等级
// //	  _cancelType domain.CancelType: 委托撤销类型
// //	  _liquidationType domain.LiquidationType: 强平类型
// //	  _reduceAmount decimal.Decimal: 减仓数量
// //
// // Deprecated
// func (bs *workingBurstService) CheckDeal(workingBurstInfo *burstPosInfo, skipRiskMargin bool, originBurstLevel int, cancelType domain.CancelType, liquidationType domain.LiquidationType, reduceAmount decimal.Decimal) {
// 	logrus.Info(0, fmt.Sprintf("%s burstService Check %s Deal", workingBurstInfo.ContractCode(), workingBurstInfo.UID), "skipRiskMargin:", skipRiskMargin, "originBurstLevel:", originBurstLevel)
// 	if bs.burstStop {
// 		logrus.Info(0, "================================= burstStop", bs.burstStop)
// 		return
// 	}
// 	// 获取用户资产信息
// 	userCache, assetInfo, err := swapcache.GetUserCacheData(workingBurstInfo.Base, workingBurstInfo.Quote, workingBurstInfo.UID)
// 	if err != nil {
// 		logrus.Error(0, workingBurstInfo.ContractCode(), workingBurstInfo.UID, "CheckDeal GetUserCacheData error:", err)
// 		return
// 	}

// 	// 获取用户仓位
// 	crossPos := make([]repository.PosSwap, 0)
// 	pCache := price.New()
// 	if assetInfo.AssetMode == futuresassetpb.AssetMode_ASSET_MODE_SINGLE {
// 		crossPos, _, err = userCache.UserPos(false, bs.quote, pCache)
// 	} else {
// 		crossPos, _, err = userCache.UserPos(true, "", pCache)
// 	}
// 	if err != nil {
// 		logrus.Error(0, bs.contractCode, "CheckDeal userCache.UserPos error:", err, workingBurstInfo.UID)
// 		return
// 	}

// 	fmt.Println("------", 1, workingBurstInfo.ContractCode(), workingBurstInfo.UID, "check start")

// 	var isolatedPos []repository.PosSwap

// 	var originPosInfo repository.PosSwap
// 	switch workingBurstInfo.MarginMode {
// 	case domain.MarginModeCross:
// 		for _, info := range crossPos {
// 			if info.PosId == workingBurstInfo.PosId {
// 				originPosInfo = info
// 				break
// 			}
// 		}

// 	case domain.MarginModeIsolated:
// 		isolatedPos = []repository.PosSwap{
// 			assetInfo.LongPos,
// 			assetInfo.ShortPos,
// 			assetInfo.BothPos,
// 		}
// 		for _, info := range isolatedPos {
// 			if info.PosId == workingBurstInfo.PosId {
// 				originPosInfo = info
// 				break
// 			}
// 		}

// 	default:

// 	}

// 	fmt.Println("------", 2, workingBurstInfo.ContractCode(), workingBurstInfo.UID, "originPosInfo.Pos", originPosInfo.Pos, "originPosInfo.PosAvailable", originPosInfo.PosAvailable)
// 	if len(originPosInfo.PosId) < 1 {
// 		logrus.Error(0, "CheckDeal pos info id not found in pos list")
// 		return
// 	}

// 	fmt.Println("------", 3, workingBurstInfo.ContractCode(), workingBurstInfo.UID, "workingBurstInfo.MarginMode == domain.MarginModeCross", workingBurstInfo.MarginMode == domain.MarginModeCross)
// 	var levelFilter repository.LevelFilter
// 	for _, configSetting := range workingBurstInfo.CoinPairSetting.MarginRateGear {
// 		if configSetting.Gear == workingBurstInfo.BurstLevel {
// 			levelFilter = repository.LevelFilter{
// 				Level:             configSetting.Gear,
// 				Lever:             configSetting.LeverMultiple,
// 				HighLimit:         configSetting.End,
// 				LowLimit:          configSetting.Start,
// 				InitMarginRate:    configSetting.InitRate,
// 				WarnMarginRate:    configSetting.WarnRate,
// 				HoldingMarginRate: configSetting.MaintenanceRate,
// 			}
// 		}
// 	}

// 	// 获取user费率
// 	userLevelRateInfo, err := GetContractUserLevelRate(assetInfo.UID)
// 	if err != nil {
// 		logrus.Error(0, bs.contractCode, assetInfo.UID, "crossBurstTrigger GetContractUserLevelRate error:", err)
// 		// 获取默认setting等级
// 		if levelInfo, ok := DefaultUserLevelRate.Map[0]; ok {
// 			logrus.Info(0, bs.contractCode, assetInfo.UID, "crossBurstTrigger use default taker rate")
// 			userLevelRateInfo.UID = assetInfo.UID
// 			userLevelRateInfo.ContractTaker = levelInfo.ContractTaker
// 		} else {
// 			logrus.Error(0, bs.contractCode, "crossBurstTrigger no level 0 taker rate:", err)
// 			return
// 		}
// 	}
// 	workingBurstInfo._updatePos(workingBurstInfo.BurstId, assetInfo, userCache, workingBurstInfo.CoinPairSetting,
// 		originPosInfo, crossPos, levelFilter, workingBurstInfo.BurstTime, pCache, userLevelRateInfo)

// 	/*
// 		if workingBurstInfo.MarginMode == domain.MarginModeCross {
// 			// 判断是否有平仓未成交
// 			if assetInfo.ShortPos.Pos.Truncate(workingBurstInfo.CoinPairSetting.AmountPrecision).
// 				Equal(assetInfo.ShortPos.PosAvailable.Truncate(workingBurstInfo.CoinPairSetting.AmountPrecision)) &&
// 				assetInfo.LongPos.Pos.Truncate(workingBurstInfo.CoinPairSetting.AmountPrecision).
// 					Equal(assetInfo.LongPos.PosAvailable.Truncate(workingBurstInfo.CoinPairSetting.AmountPrecision)) &&
// 				assetInfo.BothPos.Pos.Abs().Truncate(workingBurstInfo.CoinPairSetting.AmountPrecision).
// 					Equal(assetInfo.BothPos.PosAvailable.Abs().Truncate(workingBurstInfo.CoinPairSetting.AmountPrecision)) {
// 				logrus.Infoln("CheckDeal assetInfo.ShortPos.Pos", assetInfo.ShortPos.Pos, "assetInfo.ShortPos.PosAvailable", assetInfo.ShortPos.PosAvailable,
// 					"assetInfo.LongPos.Pos", assetInfo.LongPos.Pos, "assetInfo.LongPos.PosAvailable", assetInfo.LongPos.PosAvailable)
// 				return
// 			}
// 		} else {
// 			if workingBurstInfo.PosSide == domain.LongPos {
// 				// 判断是否有平仓未成交
// 				if assetInfo.LongPos.Pos.Truncate(workingBurstInfo.CoinPairSetting.AmountPrecision).
// 					Equal(assetInfo.LongPos.PosAvailable.Truncate(workingBurstInfo.CoinPairSetting.AmountPrecision)) {
// 					logrus.Infoln("CheckDeal assetInfo.LongPos.Pos", assetInfo.LongPos.Pos, "assetInfo.LongPos.PosAvailable", assetInfo.LongPos.PosAvailable)
// 					return
// 				}
// 			} else {
// 				// 判断是否有平仓未成交
// 				if assetInfo.ShortPos.Pos.Truncate(workingBurstInfo.CoinPairSetting.AmountPrecision).
// 					Equal(assetInfo.ShortPos.PosAvailable.Truncate(workingBurstInfo.CoinPairSetting.AmountPrecision)) {
// 					logrus.Infoln("CheckDeal assetInfo.ShortPos.Pos", assetInfo.ShortPos.Pos, "assetInfo.ShortPos.PosAvailable", assetInfo.ShortPos.PosAvailable)
// 					return
// 				}
// 			}
// 		}
// 	*/
// 	fmt.Println("------", 4, workingBurstInfo.ContractCode(), workingBurstInfo.UID, "workingBurstInfo.BurstLevel != originBurstLevel", workingBurstInfo.BurstLevel != originBurstLevel)
// 	if workingBurstInfo.BurstLevel != originBurstLevel {
// 		logrus.Error(0, "CheckDeal originBurstLevel", originBurstLevel, "!= burstInfo.BurstLevel", workingBurstInfo.BurstLevel)
// 		return
// 	}

// 	isFinishDeal := false
// 	// 如果原始仓位的仓位数等于可平数量时候，就成交完毕
// 	switch workingBurstInfo.PosSide {
// 	case domain.BothPos:
// 		assetInfo, err = userCache.Load()
// 		if err != nil {
// 			logrus.Error(0, workingBurstInfo.ContractCode(), workingBurstInfo.UID, "burstService CheckDeal userCache.Load error:", err)
// 			return
// 		}
// 		if !assetInfo.HasFrozen(workingBurstInfo.ContractCode()) {
// 			isFinishDeal = true
// 		}

// 	default:
// 		if originPosInfo.Pos.Abs().Truncate(workingBurstInfo.CoinPairSetting.AmountPrecision).
// 			Equal(originPosInfo.PosAvailable.Abs().Truncate(workingBurstInfo.CoinPairSetting.AmountPrecision)) {
// 			isFinishDeal = true
// 		}

// 	}

// 	fmt.Println("------", 5, workingBurstInfo.ContractCode(), workingBurstInfo.UID, "isFinishDeal", isFinishDeal, workingBurstInfo.PosSide)
// 	// 如果没有成交完毕
// 	if !isFinishDeal {
// 		// // 正在挂盘的平仓数量和价值ß
// 		// holdingPos := originPosInfo.Pos.Abs().Truncate(workingBurstInfo.CoinPairSetting.AmountPrecision).Sub(originPosInfo.PosAvailable.Abs().Truncate(workingBurstInfo.CoinPairSetting.AmountPrecision))
// 		// holdingPosValue := holdingPos.Mul(workingBurstInfo.MarkPrice).Truncate(workingBurstInfo.CoinPairSetting.PricePrecision)

// 		// 条件撤单（将之前挂的平仓单撤销）
// 		if workingBurstInfo.FrozenPos.GreaterThan(decimal.Zero) {
// 			success, _ := match.Service.ConditionCancel(workingBurstInfo.UID, strings.ToLower(workingBurstInfo.Base), strings.ToLower(workingBurstInfo.Quote), domain.Close, 0, int32(workingBurstInfo.MarginMode), int32(cancelType), 0)
// 			if !success {
// 			}
// 			time.Sleep(time.Second)
// 		}

// 		// 获取风险准备金钱包余额
// 		riskMargin := bs.getRiskMarginBalance()
// 		fmt.Println("============ riskMargin", riskMargin, "skipRiskMargin", skipRiskMargin)
// 		// 风险准备金负数
// 		if !skipRiskMargin && riskMargin.LessThanOrEqual(decimal.Zero) {
// 			// if liquidationType == domain.LiquidationTypeReduce {
// 			//	switch originPosInfo.PosSide {
// 			//	case domain.LongPos:
// 			//		workingBurstInfo.LongPosAmount = holdingPos
// 			//		workingBurstInfo.LongPosValue = holdingPosValue
// 			//
// 			//	case domain.ShortPos:
// 			//		workingBurstInfo.ShortPosAmount = holdingPos
// 			//		workingBurstInfo.ShortPosValue = holdingPosValue
// 			//
// 			//	case domain.BothPos:
// 			//		if originPosInfo.Pos.GreaterThan(decimal.Zero) {
// 			//			workingBurstInfo.LongPosAmount = holdingPos
// 			//			workingBurstInfo.LongPosValue = holdingPosValue
// 			//		} else if originPosInfo.Pos.LessThan(decimal.Zero) {
// 			//			workingBurstInfo.ShortPosAmount = holdingPos
// 			//			workingBurstInfo.ShortPosValue = holdingPosValue
// 			//		}
// 			//
// 			//	default:
// 			//
// 			//	}
// 			//
// 			//	//switch workingBurstInfo.PosSide {
// 			//	//case domain.LongPos:
// 			//	//	workingBurstInfo.LongPosAmount = assetInfo.LongPos.Pos.Sub(assetInfo.LongPos.PosAvailable).Truncate(workingBurstInfo.CoinPairSetting.AmountPrecision)
// 			//	//	workingBurstInfo.LongPosValue = workingBurstInfo.LongPosAmount.Mul(workingBurstInfo.MarkPrice).Truncate(workingBurstInfo.CoinPairSetting.PricePrecision)
// 			//	//
// 			//	//case domain.ShortPos:
// 			//	//	workingBurstInfo.ShortPosAmount = assetInfo.ShortPos.Pos.Sub(assetInfo.ShortPos.PosAvailable).Truncate(workingBurstInfo.CoinPairSetting.AmountPrecision)
// 			//	//	workingBurstInfo.ShortPosValue = workingBurstInfo.ShortPosAmount.Mul(workingBurstInfo.MarkPrice).Truncate(workingBurstInfo.CoinPairSetting.PricePrecision)
// 			//	//
// 			//	//default:
// 			//	//
// 			//	//}
// 			// }

// 			// 对手方强制减仓
// 			bs.ForceRivalBurst(assetInfo, workingBurstInfo, liquidationType, reduceAmount)
// 			fmt.Println("------ ForceRivalBurst")
// 			// todo 强制对手方失败处理
// 			time.Sleep(time.Second * 3)
// 			bs.CheckDeal(workingBurstInfo, skipRiskMargin, originBurstLevel, cancelType, liquidationType, reduceAmount)
// 		} else {
// 			fmt.Println("------", 6)
// 			err = bs._depth5Close(workingBurstInfo, originBurstLevel, cancelType, liquidationType, reduceAmount)
// 			if err != nil {
// 				logrus.Error(0, workingBurstInfo.ContractCode(), workingBurstInfo.UID, "CheckDeal depth5Close error:", err)
// 			}
// 			time.Sleep(time.Second * 3)
// 			fmt.Println("------", 7)
// 			bs.CheckDeal(workingBurstInfo, skipRiskMargin, originBurstLevel, cancelType, liquidationType, reduceAmount)
// 			fmt.Println("------", 8)
// 		}
// 	}
// 	fmt.Println("------", 9, workingBurstInfo.ContractCode(), workingBurstInfo.UID, "check end")
// 	return
// }

// // 取风险准备金钱包余额
// func (bs *workingBurstService) getRiskMarginBalance() decimal.Decimal {
// 	balance, err := operate.GetRiskReserve()
// 	if err != nil {
// 		logrus.Error(0, "get risk reserve error:", err)
// 		return decimal.Zero
// 	}
// 	return balance
// }

// // SelfForceBurst 自成交强制平仓（多空抵消）
// //
// //	Params:
// //	  _burstPos *burstPos: 爆仓仓位信息
// //	  _forceAmount decimal.Decimal: 强制减仓数量
// //	  _liquidationType domain.LiquidationType: 强平类型
// //
// // Deprecated
// func (bs *workingBurstService) SelfForceBurst(workingBurstInfo *burstPosInfo, assetInfo *repository.AssetSwap, forceAmount decimal.Decimal, liquidationType domain.LiquidationType) error {
// 	if forceAmount.IsZero() {
// 		if workingBurstInfo.LongPosAmount.LessThanOrEqual(workingBurstInfo.ShortPosAmount) {
// 			forceAmount = workingBurstInfo.LongPosAmount
// 		} else {
// 			forceAmount = workingBurstInfo.ShortPosAmount
// 		}
// 	}

// 	forceAmount = forceAmount.Truncate(domain.CurrencyPrecision)
// 	logrus.Info(0, "SelfForceBurst forceAmount", forceAmount)

// 	lastPrice, err := sharedcache.GetContractLastPrice(bs.base, bs.quote)
// 	if err != nil {
// 		errMsg := fmt.Sprintf("SelfForceBurst SelfForceBurst GetContractLastPrice error: %s", err)
// 		logrus.Error(0, errMsg)
// 		return errors.New(errMsg)
// 	}

// 	pricePrecision := int32(domain.PricePrecision)
// 	if bs.coinPairInfo.PricePrecision > 0 {
// 		pricePrecision = bs.coinPairInfo.PricePrecision
// 	}
// 	res, err := match.Service.BothReducePositions(&match.ReducePositionsParams{
// 		UserSide: match.SideSell,

// 		UID:                 workingBurstInfo.UID,
// 		UserLeverage:        workingBurstInfo.Leverage,
// 		UserMarginMode:      int(workingBurstInfo.MarginMode),
// 		UserLiquidationType: int(liquidationType),
// 		UserHoldMode:        assetInfo.PositionMode,
// 		UserIsMixMargin:     assetInfo.AssetMode,
// 		UserTimeInForce:     match.TimeInForceTypeGTC,

// 		TargetUserId:              workingBurstInfo.UID,
// 		TargetUserLeverage:        workingBurstInfo.Leverage,
// 		TargetUserMarginMode:      int(workingBurstInfo.MarginMode),
// 		TargetUserLiquidationType: int(liquidationType),
// 		TargetUserHoldMode:        assetInfo.PositionMode,
// 		TargetUserIsMixMargin:     assetInfo.AssetMode,
// 		TargetUserTimeInForce:     match.TimeInForceTypeGTC,

// 		AccountType: match.AccountTypeSwap,
// 		Base:        bs.base,
// 		Quote:       bs.quote,
// 		Price:       lastPrice.Truncate(pricePrecision),
// 		Amount:      forceAmount.Truncate(domain.CurrencyPrecision),
// 		Platform:    match.PlatformSystem,
// 		BurstId:     workingBurstInfo.BurstId,
// 		BurstTime:   workingBurstInfo.BurstTime,
// 	})
// 	if err != nil {
// 		errMsg := fmt.Sprintf("SelfForceBurst ForceRivalBurst BothReducePositions error: %s", err)
// 		logrus.Error(0, errMsg)
// 		err = errors.New(errMsg)
// 	} else if res.Code != 200 {
// 		errMsg := fmt.Sprintf("SelfForceBurst ForceRivalBurst BothReducePositions response error: %d -- %s", res.Code, res.Msg)
// 		logrus.Error(0, errMsg)
// 		err = errors.New(errMsg)
// 	}
// 	return err
// }

// // ForceDownPosLevel 强制部分减仓
// //
// //	Params:
// //	  _burstPos *burstPos: 爆仓仓位信息
// //	Return:
// //	  0 bool: 强制减仓成功
// //	  1 bool: 是否自成交
// //	  2 decimal.Decimal: 减仓数量
// //	  3 error: 错误信息
// //
// // Deprecated
// func (bs *workingBurstService) ForceDownPosLevel(workingBurstInfo *burstPosInfo, assetInfo *repository.AssetSwap, pCache *price.PCache) (bool, bool, decimal.Decimal, error) {
// 	positionMode := domain.HoldModeHedge
// 	if workingBurstInfo.PosSide == domain.BothPos {
// 		positionMode = domain.HoldModeBoth
// 	}

// 	isMixMargin := match.NotMixMargin
// 	if assetInfo.AssetMode == domain.AssetMode {
// 		isMixMargin = match.IsMixMargin
// 	}

// 	// 确定降级交易数量
// 	totalPosValue := workingBurstInfo.LongPosValue.Add(workingBurstInfo.ShortPosValue)
// 	nextLevelFilter, err := setting.NextMarginLevel(workingBurstInfo.Base, workingBurstInfo.Quote, workingBurstInfo.BurstLevel)
// 	if err != nil {
// 		logrus.Error(0, bs.contractCode, workingBurstInfo.UID, "ForceDownPosLevel nextLevel error:", err)
// 		return false, false, decimal.Zero, err
// 	}

// 	targetHighLimit := nextLevelFilter.HighLimit.Mul(decimal.NewFromFloat(0.99))
// 	if totalPosValue.Sub(targetHighLimit).LessThanOrEqual(decimal.Zero) {
// 		return false, false, decimal.Zero, nil
// 	}

// 	// 减仓剩余数量
// 	remainingAmount := decimal.Zero
// 	if targetHighLimit.GreaterThan(decimal.NewFromFloat(-1)) {
// 		markPrice := pCache.GetMarkPrice(bs.contractCode)
// 		if markPrice.IsZero() {
// 			logrus.Error(0, bs.contractCode, workingBurstInfo.UID, "force down level long MarkPrice is zero")
// 			return false, false, decimal.Zero, errors.New("force down level long MarkPrice is zero")
// 		}
// 		remainingAmount = targetHighLimit.DivRound(markPrice, bs.coinPairInfo.AmountPrecision).Abs()
// 	}
// 	remainingAmount = remainingAmount.Truncate(domain.CurrencyPrecision)
// 	if remainingAmount.IsZero() {
// 		remainingAmount = bs.coinPairInfo.MinAmount
// 	}

// 	longPosAmount := workingBurstInfo.LongPosAmount
// 	shortPosAmount := workingBurstInfo.ShortPosAmount

// 	if workingBurstInfo.MarginMode == domain.MarginModeCross && workingBurstInfo.PosSide != domain.BothPos {
// 		// 可以自己抵消
// 		if longPosAmount.GreaterThan(decimal.Zero) && shortPosAmount.GreaterThan(decimal.Zero) {
// 			// 全仓减仓多空抵消数量
// 			crossReduceAmount := longPosAmount.Add(shortPosAmount).Sub(remainingAmount).DivRound(decimal.NewFromFloat(2), bs.coinPairInfo.AmountPrecision)
// 			// 减仓多空抵消最大数量
// 			selfTradMaxAmount := decimal.Min(longPosAmount, shortPosAmount).Truncate(bs.coinPairInfo.AmountPrecision)
// 			if selfTradMaxAmount.GreaterThanOrEqual(crossReduceAmount) && selfTradMaxAmount.Sub(crossReduceAmount).GreaterThan(bs.coinPairInfo.MinAmount) {
// 				selfTradMaxAmount = crossReduceAmount
// 			}
// 			err := bs.SelfForceBurst(workingBurstInfo, assetInfo, selfTradMaxAmount, workingBurstInfo.LiquidationType)
// 			if err != nil {
// 				return false, true, decimal.Zero, err
// 			}
// 			longPosAmount = longPosAmount.Sub(selfTradMaxAmount)
// 			shortPosAmount = shortPosAmount.Sub(selfTradMaxAmount)
// 			return true, true, selfTradMaxAmount, nil
// 		}
// 	}

// 	totalAmount := longPosAmount.Add(shortPosAmount)
// 	// 单方向平多
// 	if longPosAmount.GreaterThan(decimal.Zero) && totalAmount.GreaterThan(remainingAmount) {
// 		tradeAmount := longPosAmount.Div(totalAmount).Mul(totalAmount.Sub(remainingAmount))
// 		pricePrecision := int32(domain.PricePrecision)
// 		if bs.coinPairInfo.PricePrecision > 0 {
// 			pricePrecision = bs.coinPairInfo.PricePrecision
// 		}
// 		// // 减仓数量回写数据库
// 		// _burstPos.LongPosAmount = tradeAmount
// 		// _burstPos.LongPosValue = tradeAmount.Mul(_burstPos.MarkPrice)
// 		// burstSwap := entity.BurstSwap{
// 		//	BurstId: _burstPos.BurstId,
// 		//	UID: _burstPos.UID,
// 		//	BurstTime: _burstPos.BurstTime,
// 		//	Level: _burstPos.BurstLevel,
// 		// }
// 		// err := burstSwap.UpdateBurstAmountAndValue(tradeAmount, tradeAmount.Mul(_burstPos.MarkPrice), domain.LongPos)
// 		// if err != nil {
// 		//	logrus.Errorln("ForceDownPosLevel long pos UpdateBurstAmountAndValue error:", err)
// 		// }
// 		// 破产价格挂单
// 		if workingBurstInfo.CollapsePrice.LessThanOrEqual(decimal.Zero) {
// 			closeResponse, err := match.Service.ClosePositions(&match.ClosePositionParams{
// 				PositionParams: match.PositionParams{
// 					Side:         domain.Sell,
// 					PosSide:      domain.Long,
// 					IsLimitOrder: match.IsLimitOrderYes,
// 					OrderType:    match.OrderTypeNormal,
// 					Amount:       tradeAmount.Truncate(domain.CurrencyPrecision),
// 					TriggerPrice: decimal.Zero,
// 				},
// 				UID:             workingBurstInfo.UID,
// 				AccountType:     match.AccountTypeSwap,
// 				Base:            bs.base,
// 				Quote:           bs.quote,
// 				Leverage:        workingBurstInfo.Leverage,
// 				Platform:        match.PlatformSystem,
// 				LiquidationType: int(workingBurstInfo.LiquidationType),
// 				MarginMode:      int(workingBurstInfo.MarginMode),
// 				TimeInForce:     match.TimeInForceTypeGTC,
// 				PositionMode:    positionMode,
// 				IsMixMargin:     isMixMargin,
// 				Depth:           5,
// 				BurstId:         workingBurstInfo.BurstId,
// 				BurstTime:       workingBurstInfo.BurstTime,
// 			})
// 			if err != nil {
// 				logrus.Error(0, "ForceDownPosLevel ClosePositions LongPos close error:", err)
// 				return false, false, decimal.Zero, err
// 			}
// 			if closeResponse.Code != 200 {
// 				logrus.Error(0, bs.contractCode, workingBurstInfo.UID, "ForceDownPosLevel LongPos CollapsePrice = 0 --> depth 5 close Response", fmt.Sprintf("%+v", closeResponse), "amount:", tradeAmount.Truncate(domain.CurrencyPrecision), err)
// 			}
// 		} else {
// 			closeResponse, err := match.Service.ClosePositions(&match.ClosePositionParams{
// 				PositionParams: match.PositionParams{
// 					Side:         domain.Sell,
// 					PosSide:      domain.Long,
// 					IsLimitOrder: match.IsLimitOrderYes,
// 					OrderType:    match.OrderTypeNormal,
// 					Price:        workingBurstInfo.CollapsePrice.Truncate(pricePrecision),
// 					Amount:       tradeAmount.Truncate(domain.CurrencyPrecision),
// 					TriggerPrice: decimal.Zero,
// 				},
// 				UID:             workingBurstInfo.UID,
// 				AccountType:     match.AccountTypeSwap,
// 				Base:            bs.base,
// 				Quote:           bs.quote,
// 				Leverage:        workingBurstInfo.Leverage,
// 				Platform:        match.PlatformSystem,
// 				LiquidationType: int(workingBurstInfo.LiquidationType),
// 				MarginMode:      int(workingBurstInfo.MarginMode),
// 				TimeInForce:     match.TimeInForceTypeGTC,
// 				PositionMode:    positionMode,
// 				IsMixMargin:     isMixMargin,
// 				BurstId:         workingBurstInfo.BurstId,
// 				BurstTime:       workingBurstInfo.BurstTime,
// 			})
// 			if err != nil {
// 				logrus.Error(0, "ForceDownPosLevel ClosePositions LongPos close error:", err)
// 				return false, false, decimal.Zero, err
// 			}
// 			if closeResponse.Code != 200 {
// 				logrus.Error(0, bs.contractCode, workingBurstInfo.UID, "ForceDownPosLevel LongPos close Response", fmt.Sprintf("%+v", closeResponse), "amount:", tradeAmount.Truncate(domain.CurrencyPrecision), err)
// 				return false, false, decimal.Zero, err
// 			}
// 		}
// 		return true, false, tradeAmount, nil
// 	}
// 	// 单方向平空
// 	if shortPosAmount.GreaterThan(decimal.Zero) && totalAmount.GreaterThan(remainingAmount) {
// 		tradeAmount := shortPosAmount.Div(totalAmount).Mul(totalAmount.Sub(remainingAmount))
// 		pricePrecision := int32(domain.PricePrecision)
// 		if bs.coinPairInfo.PricePrecision > 0 {
// 			pricePrecision = bs.coinPairInfo.PricePrecision
// 		}
// 		// // 减仓数量回写数据库
// 		// _burstPos.ShortPosPrice = tradeAmount
// 		// _burstPos.ShortPosValue = tradeAmount.Mul(_burstPos.MarkPrice)
// 		// burstSwap := entity.BurstSwap{
// 		//	BurstId: _burstPos.BurstId,
// 		//	UID: _burstPos.UID,
// 		//	BurstTime: _burstPos.BurstTime,
// 		//	Level: _burstPos.BurstLevel,
// 		// }
// 		// err := burstSwap.UpdateBurstAmountAndValue(tradeAmount, tradeAmount.Mul(_burstPos.MarkPrice), domain.ShortPos)
// 		// if err != nil {
// 		//	logrus.Errorln("ForceDownPosLevel short pos UpdateBurstAmountAndValue error:", err)
// 		// }
// 		// 破产价格挂单
// 		if workingBurstInfo.CollapsePrice.LessThanOrEqual(decimal.Zero) {
// 			closeResponse, err := match.Service.ClosePositions(&match.ClosePositionParams{
// 				PositionParams: match.PositionParams{
// 					Side:         domain.Buy,
// 					PosSide:      domain.Short,
// 					IsLimitOrder: match.IsLimitOrderYes,
// 					OrderType:    match.OrderTypeNormal,
// 					Amount:       tradeAmount.Truncate(domain.CurrencyPrecision),
// 					TriggerPrice: decimal.Zero,
// 				},
// 				UID:             workingBurstInfo.UID,
// 				AccountType:     match.AccountTypeSwap,
// 				Base:            bs.base,
// 				Quote:           bs.quote,
// 				Leverage:        workingBurstInfo.Leverage,
// 				Platform:        match.PlatformSystem,
// 				LiquidationType: int(workingBurstInfo.LiquidationType),
// 				MarginMode:      int(workingBurstInfo.MarginMode),
// 				TimeInForce:     match.TimeInForceTypeGTC,
// 				PositionMode:    positionMode,
// 				IsMixMargin:     isMixMargin,
// 				Depth:           5,
// 				BurstId:         workingBurstInfo.BurstId,
// 				BurstTime:       workingBurstInfo.BurstTime,
// 			})
// 			if err != nil {
// 				logrus.Error(0, "ForceDownPosLevel ClosePositions ShortPos close error:", err)
// 				return false, false, decimal.Zero, err
// 			}
// 			if closeResponse.Code != 200 {
// 				logrus.Error(0, bs.contractCode, workingBurstInfo.UID, "ForceDownPosLevel ShortPos CollapsePrice = 0 --> depth 5 close Response", fmt.Sprintf("%+v", closeResponse), "amount:", tradeAmount.Truncate(domain.CurrencyPrecision), err)
// 				return false, false, decimal.Zero, err
// 			}
// 		} else {
// 			closeResponse, err := match.Service.ClosePositions(&match.ClosePositionParams{
// 				PositionParams: match.PositionParams{
// 					Side:         domain.Buy,
// 					PosSide:      domain.Short,
// 					IsLimitOrder: match.IsLimitOrderYes,
// 					OrderType:    match.OrderTypeNormal,
// 					Price:        workingBurstInfo.CollapsePrice.Truncate(pricePrecision),
// 					Amount:       tradeAmount.Truncate(domain.CurrencyPrecision),
// 					TriggerPrice: decimal.Zero,
// 				},
// 				UID:             workingBurstInfo.UID,
// 				AccountType:     match.AccountTypeSwap,
// 				Base:            bs.base,
// 				Quote:           bs.quote,
// 				Leverage:        workingBurstInfo.Leverage,
// 				Platform:        match.PlatformSystem,
// 				LiquidationType: int(workingBurstInfo.LiquidationType),
// 				MarginMode:      int(workingBurstInfo.MarginMode),
// 				TimeInForce:     match.TimeInForceTypeGTC,
// 				PositionMode:    positionMode,
// 				IsMixMargin:     isMixMargin,
// 				BurstId:         workingBurstInfo.BurstId,
// 				BurstTime:       workingBurstInfo.BurstTime,
// 			})
// 			if err != nil {
// 				logrus.Error(0, "ForceDownPosLevel ClosePositions ShortPos close error:", err)
// 				return false, false, decimal.Zero, err
// 			}
// 			if closeResponse.Code != 200 {
// 				logrus.Error(0, bs.contractCode, workingBurstInfo.UID, "ForceDownPosLevel ShortPos close Response", fmt.Sprintf("%+v", closeResponse), "amount:", tradeAmount.Truncate(domain.CurrencyPrecision), err)
// 			}
// 		}
// 		return true, false, tradeAmount, nil
// 	}
// 	return false, false, decimal.Zero, nil
// }

// // Deprecated
// func (bs *workingBurstService) _cleanWorkingFlag(uid string, _posId string) {
// 	flag := bs.genWorkingFlag(_userId, _posId)
// 	bursting.Lock()
// 	delete(bursting.WorkingMap, flag)
// 	bursting.Unlock()
// 	return
// }

// // Deprecated
// func (bs *workingBurstService) _relineBurstTask(_burstPosInfo *burstPosInfo, _index string) {
// 	jsonBytes, err := json.Marshal(_burstPosInfo)
// 	if err != nil {
// 		logrus.Error(0, bs.contractCode, "burstProcessor Marshal error:", err, string(jsonBytes))
// 		return
// 	}
// 	redisCli.LPush(cachekey.GetBurstIndexListRedisKey(_burstPosInfo.ContractCode(), _burstPosInfo.MarginMode, fmt.Sprintf("%s", _index)), string(jsonBytes))
// 	return
// }

// // _cleanUserPosProcessor 清理用户仓位处理器（禁用币对时调用）
// //
// //	Params:
// //	  _ctx context.Context: 运行上下文
// //	  _wg *sync.WaitGroup: 全局wait group
// //	  _isRecover bool: 是否是recover调用
// func (bs *workingBurstService) _cleanUserPosProcessor(_ctx context.Context, _wg *sync.WaitGroup, _isRecover bool) {
// 	defer func() {
// 		if err := recover(); err != nil {
// 			bs._cleanUserPosProcessor(_ctx, _wg, true)
// 		}
// 	}()
// 	if !_isRecover {
// 		_wg.Add(1)
// 	}

// 	logrus.Info(0, bs.contractCode, "clean user pos processor started")
// 	defer logrus.Info(0, bs.contractCode, "clean user pos processor stopped")
// 	redisKey := cache.GetCloseAllUserPosListRedisKey(bs.base, bs.quote)

// Loop:
// 	for {
// 		select {
// 		case <-_ctx.Done():
// 			break Loop

// 		default:
// 			uid, err := redisCli.RPop(redisKey)
// 			if len(uid) != 0 {
// 				logrus.Info(0, bs.contractCode, "_cleanUserPosProcessor", uid)
// 			}
// 			if err != nil {
// 				if err != redis.Nil {
// 					logrus.Error(0, bs.contractCode, "cleanUserPosProcessor RPop error: %s %s", err, redisKey)
// 				}
// 				time.Sleep(time.Second)
// 				continue
// 			}
// 			burstId := util.GenerateId()
// 			burstTime := time.Now().UnixNano()
// 			bs.forceCloseUserPos(burstId, uid, burstTime, domain.CancelTypeDisableSymbol, domain.LiquidationTypeDisableSymbol)

// 		}
// 		runtime.Gosched()
// 	}

// 	_wg.Done()
// }

// // forceCloseUserPos 强制平用户仓位（暗撮）
// //
// //	Params:
// //	  _burstId string: 爆仓id
// //	  _userId string: 用户id
// //	  _burstTime int64: 爆仓时间
// //	  _cancelType domain.CancelType: 委托撤销类型
// //	  _liquidationType domain.LiquidationType: 强平类型
// func (bs *workingBurstService) forceCloseUserPos(_burstId, uid string, _burstTime int64, _cancelType domain.CancelType, _liquidationType domain.LiquidationType) {
// 	if bs.burstStop {
// 		return
// 	}
// 	totalLongPos, totalShortPos, err := swapcache.GetTotalPosAmount(bs.base, bs.quote)
// 	if err != nil {
// 		logrus.Error(0, bs.contractCode, "forceCloseUserPos GetTotalPosAmount error:", err, "liquidationType", _liquidationType)
// 		return
// 	}
// 	logrus.Info(0, bs.contractCode, "forceCloseUserPos total pos totalLongPos", totalLongPos, "totalShortPos", totalShortPos, "liquidationType", _liquidationType)

// 	totalTrialLongPos, totalTrialShortPos, err := swapcache.GetTotalTrialPosAmount(bs.base, bs.quote)
// 	if err != nil {
// 		logrus.Error(0, bs.contractCode, "forceCloseUserPos GetTotalTrialPosAmount error:", err, "liquidationType", _liquidationType)
// 		return
// 	}
// 	logrus.Info(0, bs.contractCode, "forceCloseUserPos total pos totalTrialLongPos", totalTrialLongPos, "totalTrialShortPos", totalTrialShortPos, "liquidationType", _liquidationType)

// 	totalLongPos = totalLongPos.Add(totalTrialLongPos)
// 	totalShortPos = totalShortPos.Add(totalTrialShortPos)

// 	if !totalShortPos.Equal(totalLongPos) {
// 		// todo notice developer
// 		logrus.Error(0, bs.contractCode, "forceCloseUserPos total pos not equal: totalLongPos", totalLongPos, "totalShortPos", totalShortPos, "liquidationType", _liquidationType)
// 		return
// 	}
// 	// 获取资产信息
// 	userCache, assetInfo, err := bs.GetUserCacheAndAssetInfo(_userId)
// 	if err != nil {
// 		logrus.Error(0, bs.contractCode, "processCloseUserPos error: %s %s", err, _userId)
// 		return
// 	}
// 	// 更新对手方评分
// 	bs.UpdateRivalScore(_userId, assetInfo, userCache)
// 	// 确定保证金模式
// 	marginMode := domain.MarginModeIsolated
// 	// 真实仓位
// 	longPos := assetInfo.LongPos
// 	shortPos := assetInfo.ShortPos
// 	bothPos := assetInfo.BothPos

// 	// 体验金仓位
// 	trialLongPos := assetInfo.TrialLongPos
// 	trialShortPos := assetInfo.TrialShortPos
// 	trialBothPos := assetInfo.TrialBothPos

// 	totalPosAmount := longPos.Pos.Add(shortPos.Pos.Add(bothPos.Pos.Abs()))
// 	totalPosAmount = totalPosAmount.Add(trialLongPos.Pos.Add(trialShortPos.Pos.Add(trialBothPos.Pos.Abs())))

// 	// 如果仓位为零，结束平仓
// 	if totalPosAmount.LessThanOrEqual(decimal.Zero) {
// 		return
// 	}

// 	switch {
// 	case longPos.Pos.GreaterThan(decimal.Zero):
// 		marginMode = longPos.GetMarginMode()
// 	case shortPos.Pos.GreaterThan(decimal.Zero):
// 		marginMode = shortPos.GetMarginMode()
// 	case bothPos.Pos.Abs().GreaterThan(decimal.Zero):
// 		marginMode = bothPos.GetMarginMode()
// 	case trialLongPos.Pos.GreaterThan(decimal.Zero):
// 		marginMode = trialLongPos.GetMarginMode()
// 	case trialShortPos.Pos.GreaterThan(decimal.Zero):
// 		marginMode = trialShortPos.GetMarginMode()
// 	case trialBothPos.Pos.Abs().GreaterThan(decimal.Zero):
// 		marginMode = trialBothPos.GetMarginMode()
// 	}

// 	// 撤销用户平仓单
// 	if longPos.Pos.Add(shortPos.Pos.Add(bothPos.Pos.Abs())).Sub(longPos.PosAvailable.Add(shortPos.PosAvailable.Add(bothPos.PosAvailable.Abs()))).GreaterThan(decimal.Zero) {
// 		_, _ = match.Service.ConditionCancel(_userId, strings.ToLower(bs.base), strings.ToLower(bs.quote), domain.Close, 0, int32(marginMode), int32(_cancelType), 0)
// 		time.Sleep(500 * time.Millisecond)
// 	}
// 	// 获取最新成交价
// 	lastPrice, err := sharedcache.GetContractLastPrice(bs.base, bs.quote)
// 	if err != nil {
// 		logrus.Error(0, "processCloseUserPos GetContractLastPrice error:", err)
// 		return
// 	}

// 	pricePrecision := int32(domain.PricePrecision)
// 	if bs.coinPairInfo.PricePrecision > 0 {
// 		pricePrecision = bs.coinPairInfo.PricePrecision
// 	}

// 	tradeAmount := decimal.Zero
// 	tradeSide := match.SideBuy
// 	var makerPos repository.PosSwap
// 	var takerPos repository.PosSwap
// 	// 多空自抵消
// 	if longPos.Pos.GreaterThan(decimal.Zero) && shortPos.Pos.GreaterThan(decimal.Zero) {
// 		makerPos = longPos
// 		takerPos = shortPos
// 		tradeAmount = decimal.Min(longPos.Pos, shortPos.Pos)
// 	} else {
// 		// 只平多仓
// 		if longPos.Pos.GreaterThan(decimal.Zero) {
// 			// takerPos 改为平仓仓位
// 			takerPos = longPos
// 			tradeSide = match.SideSell
// 			rivalListKey := cachekey.GetBurstRivalShortRankRedisKey(bs.contractCode)

// 			rivalUsers, err := redisCli.ZRevRangeByScoreWithScores(rivalListKey, 0, 500)
// 			if err != nil {
// 				logrus.Error(0, "processCloseUserPos ZRevRangeByScoreWithScores error:", err)
// 				return
// 			}
// 			for _, userInfo := range rivalUsers {
// 				if rivalUserId, ok := userInfo.Member.(string); ok {
// 					isTrialPos := false
// 					if swapcache.UserIdIsContainTrial(rivalUserId) {
// 						isTrialPos = true
// 						rivalUserId = swapcache.RemoveUserIdTrial(rivalUserId)
// 					}
// 					_, rivalAssetInfo, err := bs.GetUserCacheAndAssetInfo(rivalUserId)
// 					if err != nil {
// 						logrus.Error(0, bs.contractCode, "rival processCloseUserPos error:", err, rivalUserId)
// 						continue
// 					}
// 					if rivalAssetInfo.PositionMode == domain.HoldModeBoth {
// 						makerPos = rivalAssetInfo.BothPos
// 						if isTrialPos {
// 							makerPos = rivalAssetInfo.TrialBothPos
// 						}
// 					} else {
// 						makerPos = rivalAssetInfo.ShortPos
// 						if isTrialPos {
// 							makerPos = rivalAssetInfo.TrialShortPos
// 						}
// 					}
// 					// 对手方无仓位直接跳过
// 					if makerPos.Pos.LessThanOrEqual(decimal.Zero) {
// 						continue
// 					}
// 					// 撤销用户平仓单
// 					if makerPos.Pos.Sub(makerPos.PosAvailable).GreaterThan(decimal.Zero) {
// 						_, _ = match.Service.ConditionCancel(makerPos.UID, strings.ToLower(bs.base), strings.ToLower(bs.quote), domain.Close, 0, int32(makerPos.GetMarginMode()), int32(_cancelType), 0)
// 						time.Sleep(500 * time.Millisecond)
// 					}
// 					tradeAmount = decimal.Min(makerPos.Pos, takerPos.Pos)
// 					break
// 				}
// 			}
// 		}
// 		// 只有空仓
// 		if shortPos.Pos.GreaterThan(decimal.Zero) {
// 			// takerPos 改为平仓仓位
// 			takerPos = shortPos
// 			rivalListKey := cachekey.GetBurstRivalLongRankRedisKey(bs.contractCode)

// 			rivalUsers, err := redisCli.ZRevRangeByScoreWithScores(rivalListKey, 0, 500)
// 			if err != nil {
// 				logrus.Error(0, "processCloseUserPos ZRevRangeByScoreWithScores error:", err)
// 				return
// 			}
// 			for _, userInfo := range rivalUsers {
// 				if rivalUserId, ok := userInfo.Member.(string); ok {
// 					isTrialPos := false
// 					if swapcache.UserIdIsContainTrial(rivalUserId) {
// 						isTrialPos = true
// 						rivalUserId = swapcache.RemoveUserIdTrial(rivalUserId)
// 					}
// 					_, rivalAssetInfo, err := bs.GetUserCacheAndAssetInfo(rivalUserId)
// 					if err != nil {
// 						logrus.Error(0, bs.contractCode, "rival processCloseUserPos error: %s %s", err, rivalUserId)
// 						continue
// 					}
// 					if rivalAssetInfo.PositionMode == domain.HoldModeBoth {
// 						makerPos = rivalAssetInfo.BothPos
// 						if isTrialPos {
// 							makerPos = rivalAssetInfo.TrialBothPos
// 						}
// 					} else {
// 						makerPos = rivalAssetInfo.LongPos
// 						if isTrialPos {
// 							makerPos = rivalAssetInfo.TrialLongPos
// 						}
// 					}
// 					// 对手方无仓位直接跳过
// 					if makerPos.Pos.LessThanOrEqual(decimal.Zero) {
// 						continue
// 					}
// 					// 撤销用户平仓单
// 					if makerPos.Pos.Sub(makerPos.PosAvailable).GreaterThan(decimal.Zero) {
// 						_, _ = match.Service.ConditionCancel(makerPos.UID, strings.ToLower(bs.base), strings.ToLower(bs.quote), domain.Close, 0, int32(makerPos.GetMarginMode()), int32(_cancelType), 0)
// 						time.Sleep(500 * time.Millisecond)
// 					}
// 					tradeAmount = decimal.Min(makerPos.Pos, takerPos.Pos)
// 					break
// 				}
// 			}
// 		}
// 		// 只有单向持仓
// 		if !bothPos.Pos.Equal(decimal.Zero) {
// 			// takerPos 改为平仓仓位
// 			takerPos = bothPos
// 			rivalListKey := cachekey.GetBurstRivalLongRankRedisKey(bs.contractCode)
// 			if bothPos.Pos.GreaterThan(decimal.Zero) {
// 				tradeSide = match.SideSell
// 				rivalListKey = cachekey.GetBurstRivalShortRankRedisKey(bs.contractCode)
// 			}

// 			rivalUsers, err := redisCli.ZRevRangeByScoreWithScores(rivalListKey, 0, 500)
// 			if err != nil {
// 				logrus.Error(0, "processCloseUserPos ZRevRangeByScoreWithScores error:", err)
// 				return
// 			}
// 			for _, userInfo := range rivalUsers {
// 				if rivalUserId, ok := userInfo.Member.(string); ok {
// 					isTrialPos := false
// 					if swapcache.UserIdIsContainTrial(rivalUserId) {
// 						isTrialPos = true
// 						rivalUserId = swapcache.RemoveUserIdTrial(rivalUserId)
// 					}
// 					_, rivalAssetInfo, err := bs.GetUserCacheAndAssetInfo(rivalUserId)
// 					if err != nil {
// 						logrus.Error(0, bs.contractCode, "rival processCloseUserPos error:", err, rivalUserId)
// 						continue
// 					}
// 					if rivalAssetInfo.PositionMode == domain.HoldModeBoth {
// 						makerPos = rivalAssetInfo.BothPos
// 						if isTrialPos {
// 							makerPos = rivalAssetInfo.TrialBothPos
// 						}
// 					} else {
// 						if isTrialPos {
// 							makerPos = rivalAssetInfo.TrialLongPos
// 							if bothPos.Pos.GreaterThan(decimal.Zero) {
// 								makerPos = rivalAssetInfo.TrialShortPos
// 							}
// 						} else {
// 							makerPos = rivalAssetInfo.LongPos
// 							if bothPos.Pos.GreaterThan(decimal.Zero) {
// 								makerPos = rivalAssetInfo.ShortPos
// 							}
// 						}
// 					}

// 					// 对手方无仓位直接跳过
// 					if makerPos.Pos.LessThanOrEqual(decimal.Zero) {
// 						continue
// 					}
// 					// 撤销用户平仓单
// 					if makerPos.Pos.Sub(makerPos.PosAvailable).GreaterThan(decimal.Zero) {
// 						_, _ = match.Service.ConditionCancel(makerPos.UID, strings.ToLower(bs.base), strings.ToLower(bs.quote), domain.Close, 0, int32(makerPos.GetMarginMode()), int32(_cancelType), 0)
// 						time.Sleep(500 * time.Millisecond)
// 					}
// 					tradeAmount = decimal.Min(makerPos.Pos, takerPos.Pos.Abs())
// 					break
// 				}
// 			}
// 		}
// 	}
// 	// 交易数量大于零
// 	if tradeAmount.GreaterThan(decimal.Zero) {
// 		bs._rivalCloseTrade(_burstId, _burstTime, lastPrice.Truncate(pricePrecision), tradeAmount, tradeSide, takerPos, makerPos, _liquidationType)
// 	}
// 	defer bs.forceCloseUserPos(_burstId, _userId, _burstTime, _cancelType, _liquidationType)
// }

// // Deprecated
// func (bs *workingBurstService) _rivalCloseTrade(_burstId string, _burstTime int64, _tradePrice, _tradeAmount decimal.Decimal, _tradeSide int, _takerPos, _makerPos repository.PosSwap, _liquidationType domain.LiquidationType) {
// 	userLiquidationType := _liquidationType
// 	targetUserLiquidationType := _liquidationType
// 	switch _liquidationType {
// 	case domain.LiquidationTypeBurst:
// 		fallthrough
// 	case domain.LiquidationTypeReduce:
// 		if _takerPos.UID != _makerPos.UID {
// 			targetUserLiquidationType = domain.LiquidationTypeStopProfitReduce
// 		}
// 	}

// 	ContractSettings.RLock()
// 	settingInfo, settingOk := ContractSettings.Map[_makerPos.ContractCode]
// 	ContractSettings.RUnlock()
// 	if !settingOk {
// 		logrus.Error(0, "composeCurrentUserBurstPosByPosInfo get", _makerPos.ContractCode, "BurstSettings is empty")
// 		return
// 	}

// 	// 交易数量大于零
// 	if _tradeAmount.GreaterThan(decimal.Zero) {
// 		makerBurstPos, _ := bs._composeCurrentUserBurstPosByPosSide(bs.base, bs.quote, _makerPos.UID, _burstId, _makerPos.PosSide, settingInfo, _makerPos.IsTrial())
// 		makerBurstPos.LiquidationType = domain.LiquidationTypeDisableSymbol
// 		bs._sendBurstLog(*makerBurstPos, _makerPos, decimal.Zero, decimal.Zero, false)

// 		if _takerPos.UID != _makerPos.UID {
// 			takerBurstPos, _ := bs._composeCurrentUserBurstPosByPosSide(bs.base, bs.quote, _takerPos.UID, _burstId, _takerPos.PosSide, settingInfo, _makerPos.IsTrial())
// 			takerBurstPos.LiquidationType = domain.LiquidationTypeDisableSymbol
// 			bs._sendBurstLog(*takerBurstPos, _takerPos, decimal.Zero, decimal.Zero, false)
// 		}

// 		// 获取用户资产信息
// 		_, makerAssetInfo, err := swapcache.GetUserCacheData(bs.base, bs.quote, _makerPos.UID)
// 		if err != nil {
// 			logrus.Error(0, bs.contractCode, _makerPos.UID, "rivalCloseTrade GetUserCacheData maker error:", err)
// 			time.Sleep(time.Millisecond * 200)
// 			return
// 		}

// 		// 获取用户资产信息
// 		_, takerAssetInfo, err := swapcache.GetUserCacheData(bs.base, bs.quote, _takerPos.UID)
// 		if err != nil {
// 			logrus.Error(0, bs.contractCode, _takerPos.UID, "rivalCloseTrade GetUserCacheData taker error:", err)
// 			time.Sleep(time.Millisecond * 200)
// 			return
// 		}

// 		params := &match.ReducePositionsParams{
// 			UserSide: _tradeSide,

// 			UID:                 _takerPos.UID,
// 			UserLeverage:        _takerPos.Leverage,
// 			UserMarginMode:      int(_takerPos.MarginMode),
// 			UserLiquidationType: int(userLiquidationType),
// 			UserHoldMode:        takerAssetInfo.PositionMode,
// 			UserIsMixMargin:     takerAssetInfo.AssetMode,
// 			UserTimeInForce:     match.TimeInForceTypeGTC,
// 			UserAwardOpIds:      _takerPos.AwardIds,

// 			TargetUserId:              _makerPos.UID,
// 			TargetUserLeverage:        _makerPos.Leverage,
// 			TargetUserMarginMode:      int(_makerPos.MarginMode),
// 			TargetUserLiquidationType: int(targetUserLiquidationType),
// 			TargetUserHoldMode:        makerAssetInfo.PositionMode,
// 			TargetUserIsMixMargin:     makerAssetInfo.AssetMode,
// 			TargetUserTimeInForce:     match.TimeInForceTypeGTC,
// 			TargetUserAwardOpIds:      _makerPos.AwardIds,

// 			AccountType: match.AccountTypeSwap,
// 			Base:        bs.base,
// 			Quote:       bs.quote,
// 			Price:       _tradePrice,
// 			Amount:      _tradeAmount.Truncate(domain.CurrencyPrecision),
// 			Platform:    match.PlatformSystem,
// 			BurstId:     _burstId,
// 			BurstTime:   _burstTime,
// 		}
// 		res, err := match.Service.BothReducePositions(params)
// 		if err != nil {
// 			logrus.Error(0, "rivalCloseTrade BothReducePositions error:", err)
// 		} else if res.Code != 200 {
// 			logrus.Error(0, "rivalCloseTrade BothReducePositions response error:", res.Code, "---", res.Msg, fmt.Sprintf("%+v", params))
// 			time.Sleep(time.Millisecond * 300)
// 		}
// 	}
// }

// func processorStop(_symbolList []string) {
// 	for _, symbol := range _symbolList {
// 		snapProcessorContext.Lock()
// 		ctxCancelList, ok := snapProcessorContext.Map[symbol]
// 		snapProcessorContext.Unlock()
// 		if ok {
// 			for _, ctxCancel := range ctxCancelList {
// 				ctxCancel()
// 			}
// 			snapProcessorContext.Lock()
// 			delete(snapProcessorContext.Map, symbol)
// 			snapProcessorContext.Unlock()
// 		}

// 		RunningServices.RLock()
// 		_, hasBurstWorker := RunningServices.Map[strings.ToUpper(symbol)]
// 		RunningServices.RUnlock()
// 		if hasBurstWorker {
// 			RunningServices.Lock()
// 			delete(RunningServices.Map, symbol)
// 			RunningServices.Unlock()
// 		}
// 	}
// }
