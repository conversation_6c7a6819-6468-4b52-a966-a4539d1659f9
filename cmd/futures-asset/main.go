package main

import (
	"context"
	"fmt"
	"os"
	"syscall"
	"time"

	"futures-asset/configs"
	"futures-asset/internal/delivery"
	"futures-asset/internal/libs/logs"

	"github.com/grafana/pyroscope-go"
	log "github.com/sirupsen/logrus"
	"github.com/spf13/pflag"
	cfg "yt.com/backend/common.git/config"
	"yt.com/backend/common.git/shutdown"
)

var (
	Version string

	Built string

	GitCommit string
)

const defaultShutdownTimeout = 10 * time.Second

func main() {
	var version bool

	pflag.BoolVarP(&version, "version", "v", false, "print build version")
	pflag.Parse()

	if version {
		log.Printf(" Version: %s \n Built: %s \n Git Commit: %s\n", Version, Built, GitCommit)
	} else {
		start()
	}
}

func start() {
	mainCtx, mainStopCtx := context.WithCancel(context.Background())

	appCfg, err := cfg.LoadWithEnv[configs.Config]("config", "")
	if err != nil {
		panic(fmt.Sprintf("config.LoadWithEnv, err: %s", err))
	}

	if err := logs.New(appCfg); err != nil {
		panic(fmt.Sprintf("logs.New, err: %s", err))
	}

	podName := os.Getenv("POD_NAME")
	if podName == "" {
		podName = appCfg.Env.ServiceName
	}

	if appCfg.Observability.Pyroscope.Enable {
		_, err := pyroscope.Start(pyroscope.Config{
			ApplicationName: appCfg.Env.ServiceName,
			ServerAddress:   appCfg.Observability.Pyroscope.URL,
			Logger:          log.StandardLogger(),
			ProfileTypes: []pyroscope.ProfileType{
				pyroscope.ProfileCPU,

				pyroscope.ProfileInuseObjects,
				pyroscope.ProfileAllocObjects,
				pyroscope.ProfileInuseSpace,
				pyroscope.ProfileAllocSpace,

				pyroscope.ProfileGoroutines,

				pyroscope.ProfileMutexCount,
				pyroscope.ProfileMutexDuration,

				pyroscope.ProfileBlockCount,
				pyroscope.ProfileBlockDuration,
			},
		})
		if err != nil {
			log.WithFields(log.Fields{
				"err":      err,
				"pod_name": podName,
			}).Error("pyroscope.Start fail")
			panic(fmt.Sprintf("Pyroscope start err: %s", err))
		}
	}

	shutdownHandler := shutdown.New(log.StandardLogger(), shutdown.WithGraceDuration(defaultShutdownTimeout))

	apiServer := delivery.NewAPI(appCfg, shutdownHandler)

	if err := apiServer.Start(mainCtx); err != nil {
		log.WithFields(log.Fields{
			"err": err,
		}).Error("api.Start")

		panic(fmt.Sprintf("api server start err: %s", err))
	}
	log.Info("apiServer.Start success")

	if err := shutdownHandler.Listen(
		mainCtx,
		syscall.SIGHUP,
		syscall.SIGINT,
		syscall.SIGTERM,
		syscall.SIGQUIT,
	); err != nil {
		log.WithFields(log.Fields{
			"err": err,
		}).Error("graceful shutdown failed.. forcing exit.")
	}

	mainStopCtx()

	log.WithFields(log.Fields{
		"shutdownTimeout": fmt.Sprintf("%ds", defaultShutdownTimeout/time.Second),
	}).Info("server exiting")
}
