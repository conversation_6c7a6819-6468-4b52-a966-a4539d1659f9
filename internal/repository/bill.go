package repository

import (
	"context"
	"strings"

	"futures-asset/internal/domain"
	"futures-asset/internal/domain/entity"
	"futures-asset/internal/domain/repository"
	"futures-asset/util"

	"gorm.io/gorm"

	"go.uber.org/dig"
)

type BillParam struct {
	dig.In

	DB *gorm.DB `name:"db"`
}

type billRepository struct {
	db *gorm.DB
}

func NewBillRepository(param BillParam) repository.BillRepository {
	return &billRepository{
		db: param.DB,
	}
}

// Insert implements repository.BillAssetRepository.
func (repo *billRepository) InsertAsset(ctx context.Context, billAsset *entity.BillAsset) (err error) {
	tableName := billAsset.TableName()
	if ok := util.TableIsExit(tableName); !ok {
		if !repo.db.Migrator().HasTable(tableName) {
			err := repo.db.Migrator().CreateTable(billAsset)
			if err != nil {
				return err
			}
			util.SetNewTableName(tableName)
		}
	}

	if err := repo.db.Table(tableName).Create(billAsset).Error; err != nil {
		return err
	}

	return nil
}

// InsertRobot implements repository.BillAssetRepository.
func (repo *billRepository) InsertRobotAsset(ctx context.Context, billAsset *entity.BillAsset) (err error) {
	tableName := billAsset.RobotTableName(billAsset.UID)
	if ok := util.TableIsExit(tableName); !ok {
		if !repo.db.Migrator().HasTable(tableName) {
			err := repo.db.Migrator().CreateTable(billAsset)
			if err != nil {
				return err
			}
			util.SetNewTableName(tableName)
		}
	}

	if err := repo.db.Table(tableName).Create(billAsset).Error; err != nil {
		return err
	}

	return nil
}

// InsertFundingFee implements repository.BillRepository.
func (repo *billRepository) InsertFundingFee(ctx context.Context, billFundingFee *entity.LogFundingFee) (err error) {
	return repo.db.Table(billFundingFee.TableName()).Create(billFundingFee).Error
}

// InsertFundingRate implements repository.BillRepository.
func (repo *billRepository) InsertFundingRate(ctx context.Context, billFundingRate *entity.LogFundingRate) (err error) {
	return repo.db.Table(billFundingRate.TableName()).Create(billFundingRate).Error
}

// InsertOption implements repository.BillRepository.
func (repo *billRepository) InsertOption(ctx context.Context, billOption *entity.BillOption) (err error) {
	tableName := billOption.TableName()
	if billOption.OptionType == domain.OptionTypeDemo {
		tableName = billOption.DemoTableName()
	}
	if ok := util.TableIsExit(tableName); !ok {
		if !repo.db.Migrator().HasTable(tableName) {
			err := repo.db.Migrator().CreateTable(billOption)
			if err != nil {
				return err
			}
			util.SetNewTableName(tableName)
		}
	}

	if err := repo.db.Table(tableName).Create(billOption).Error; err != nil {
		return err
	}

	return nil
}

// InsertBurst implements repository.BillRepository.
func (repo *billRepository) InsertBurst(ctx context.Context, billBurst *entity.LogBurstSwap) (err error) {
	tableName := billBurst.TableName()
	if ok := util.TableIsExit(tableName); !ok {
		if !repo.db.Migrator().HasTable(tableName) {
			err := repo.db.Migrator().CreateTable(billBurst)
			if err != nil {
				return err
			}
			util.SetNewTableName(tableName)
		}
	}

	if err := repo.db.Table(tableName).Create(billBurst).Error; err != nil {
		return err
	}

	return nil
}

// InsertRivalBurst implements repository.BillRepository.
func (repo *billRepository) InsertRivalBurst(ctx context.Context, billRivalBurst *entity.RivalBurst) (err error) {
	tableName := billRivalBurst.TableName()
	if !repo.db.Migrator().HasTable(tableName) {
		err := repo.db.Migrator().CreateTable(billRivalBurst)
		if err != nil {
			return err
		}
	}

	if err := repo.db.Table(tableName).Create(billRivalBurst).Error; err != nil {
		return err
	}

	return nil
}

func (repo *billRepository) GetBillAsset(ctx context.Context, req *repository.AssetBillParam) (int64, []*entity.BillAsset, error) {
	bills := make([]*entity.BillAsset, 0)

	// 构建查询条件
	db := repo.db.Table(new(entity.BillAsset).TableName()).Where("1 = 1")

	// 添加基本查询条件
	if len(req.UID) > 0 {
		db = db.Where("uid = ?", req.UID)
	}
	if len(req.Currency) > 0 {
		db = db.Where("currency = ?", strings.ToUpper(req.Currency))
	}
	if len(req.Symbol) > 0 {
		db = db.Where("symbol = ?", strings.ToUpper(req.Symbol))
	}

	// 处理账单类型条件
	if req.BillType > 0 {
		db = db.Where("bill_type = ?", req.BillType)
	} else {
		var billTypes []int
		if req.Plat == domain.DepartmentOperate {
			billTypes = []int{domain.BillTypeFeeTrial, domain.BillTypeFundingTrial, domain.BillTypePlatFeeTrial, domain.BillTypeRealTrial, domain.BillTrialAssetRecycle}
		} else {
			billTypes = []int{
				domain.BillTypeFee, domain.BillTypeFeeTrial, domain.BillTypeFunding, domain.BillTypeFundingTrial, domain.BillTypePlatFee, domain.BillTypePlatFeeTrial,
				domain.BillTypeReal, domain.BillTypeRealTrial, domain.BillTrialAssetAdd, domain.BillTrialAssetRecycle, domain.BillTypeInnerIn, domain.BillTypeInnerOut, domain.BillTypeDeductProfitReal,
				domain.BillTypeDeductAdd,
			}
		}
		db = db.Where("bill_type IN (?)", billTypes)
	}

	// 处理体验金查询
	if req.IsTrial == 1 {
		trialBillTypes := []int{domain.BillTypeFeeTrial, domain.BillTypeFundingTrial, domain.BillTypePlatFeeTrial, domain.BillTypeRealTrial, domain.BillTrialAssetRecycle, domain.BillTypeDeductProfitReal}
		db = db.Where("bill_type IN (?)", trialBillTypes)
	}

	// 处理时间范围
	if req.StartTime > 0 {
		db = db.Where("create_time >= ?", req.StartTime*1e9)
	}
	if req.EndTime > 0 {
		db = db.Where("create_time <= ?", req.EndTime*1e9)
	}

	// 获取总记录数
	var total int64
	if err := db.Count(&total).Error; err != nil {
		return total, bills, err
	}

	// 查询数据
	if total > 0 {
		// 添加分页和排序
		db = db.Order("create_time DESC").Offset((req.PageIndex - 1) * req.PageSize).Limit(req.PageSize)

		// 执行查询
		if err := db.Find(&bills).Error; err != nil {
			return total, bills, err
		}
	}

	return total, bills, nil
}
