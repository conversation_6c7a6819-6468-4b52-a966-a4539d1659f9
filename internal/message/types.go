package message

import (
	"github.com/shopspring/decimal"
)

// 服务间数据推送
type PushData struct {
	QueueIndex string      `json:"queueIndex"`
	Topic      string      `json:"topic"`
	UID        string      `json:"uid"`
	Data       interface{} `json:"data"`
}

// 杠杆倍数及仓位模式推送数据
type LeveragePushData struct {
	Symbol     string `json:"symbol"`
	Leverage   int    `json:"leverage"`
	LLeverage  int    `json:"lLeverage"`
	SLeverage  int    `json:"sLeverage"`
	BLeverage  int    `json:"bLeverage"`
	MarginMode int    `json:"marginMode"`
	UID        string `json:"uid"`
}

type AssetMsg struct {
	Currency     string                     `json:"currency"`
	Balance      decimal.Decimal            `json:"balance"`
	TrialBalance decimal.Decimal            `json:"trialBalance"`
	Frozen       map[string]decimal.Decimal `json:"frozen"`
}
