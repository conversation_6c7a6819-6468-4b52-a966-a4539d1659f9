package logs

import (
	"fmt"

	log "github.com/sirupsen/logrus"

	"futures-asset/configs"

	"yt.com/backend/common.git/config"
	"yt.com/backend/common.git/logger"
)


func New(cfg *config.Config[configs.Config]) error {
	err := logger.NewLog(&cfg.Env.Log, logger.WithRotateLfs(&cfg.Env.Log))
	if err != nil {
		return fmt.Errorf("logger.NewLog error: %w", err)
	}

	// listen config change
	cfg.WatchReload(func(c *config.Config[configs.Config]) {
		newLevel, err := log.ParseLevel(c.Env.Log.Level)
		if err != nil {
			log.WithFields(log.Fields{
				"newLogLevel": c.Env.Log.Level,
				"oldLogLevel": cfg.Env.Log.Level,
				"err":         err,
			}).Error("config.WatchReload")
		}

		log.SetLevel(newLevel)
	})

	return nil
}
