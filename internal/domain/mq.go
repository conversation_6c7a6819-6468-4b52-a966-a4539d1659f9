package domain

// exchange
const (
	ContractExchange       = "WalletContractExchange" // 合约的 exchange
	PushServerExchangeName = "contract.push.topic"    // 推送服务 exchange 名称
	NotifyExchange         = "notify_exchange"        // 通知交换机
)

// queue
const (
	ContractOperateQueue       = "ContractOperateQueue"  // 合约账单通知运营的队列
	ContractAddTrialAssetQueue = "ContractAddTrialAsset" // 增加体验金
)

// queue bind key
const (
	ContractAssetBindKey     = "ContractAssetBindKey"     // 合约账单通知后台的key
	ContractAddTrialAssetKey = "ContractAddTrialAssetKey" // 增加体验金
	ContractTradeBindKey     = "ContractTradeKey"         // 合约成交 bind key

	BindKeyMailGeneral = "notify_general_mail_key" // 通用邮件绑定key
	BindKeySmsGeneral  = "notify_general_sms_key"  // 通用短信绑定key
)

const (
	AssetsBillTopic        = "assetsbill"
	OptionBillTopic        = "optionbill"
	FundingFeeTopic        = "fundingfee"
	FundingRateTopic       = "fundingrate"
	TrialAssetTopic        = "trialasset"
	TrialAssetAddTopic     = "trialassetadd"
	TrialAssetRecycleTopic = "trialassetrecycle"
)
