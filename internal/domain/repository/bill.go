package repository

import (
	"context"
	"encoding/json"
	"strings"
	"time"

	"futures-asset/internal/domain/entity"
	"futures-asset/internal/libs/pager"
	"futures-asset/util"

	"github.com/shopspring/decimal"
)

type BillRepository interface {
	InsertAsset(ctx context.Context, billAsset *entity.BillAsset) (err error)
	InsertRobotAsset(ctx context.Context, billAsset *entity.BillAsset) (err error)
	InsertOption(ctx context.Context, billOption *entity.BillOption) (err error)
	InsertFundingFee(ctx context.Context, billFundingFee *entity.LogFundingFee) (err error)
	InsertFundingRate(ctx context.Context, billFundingRate *entity.LogFundingRate) (err error)
	InsertBurst(ctx context.Context, billBurst *entity.LogBurstSwap) (err error)
	InsertRivalBurst(ctx context.Context, billRivalBurst *entity.RivalBurst) (err error)

	GetBillAsset(ctx context.Context, req *AssetBillParam) (int64, []*entity.BillAsset, error)
}

type AssetBillParam struct {
	pager.Condition
	Symbol    string `json:"symbol"`
	Currency  string `json:"currency"`   // 币种
	BillType  int    `json:"bill_type"`  // 类型
	StartTime int64  `json:"start_time"` // 开始时间
	EndTime   int64  `json:"end_time"`   // 结束时间
	UID       string `json:"uid"`        // 用户id
	Plat      int    `json:"plat"`       // plat 0.前端app,web或H5，1.财务部门 2.客服部门 3.运营部门
	IsTrial   int8   `json:"is_trial"`   // 是否有体验金 0.全部 1.有
}

type BillAssetSync struct {
	entity.BillAsset
}

// MarshalBinary implement encoding.BinaryMarshaler for redis
func (slf BillAssetSync) MarshalBinary() ([]byte, error) {
	return json.Marshal(slf)
}

// UnmarshalBinary implement encoding.BinaryUnmarshaler for redis
func (slf BillAssetSync) UnmarshalBinary(data []byte) error {
	return json.Unmarshal(data, &slf)
}

// NewBillAssetSync 构建账单记账实体
//   - billType: web端展示的账单类型
func NewBillAssetSync(mqTrial *MqCmsAsset, billType int, amount decimal.Decimal) *BillAssetSync {
	billSwap := BillAssetSync{
		BillAsset: entity.BillAsset{
			UID:   mqTrial.UID,
			RefID: mqTrial.OrderId,
			// BillId:       util.GenerateId(), // TODO snowflakeID
			Symbol:     strings.ToUpper(mqTrial.Symbol),
			Currency:   strings.ToUpper(mqTrial.Currency),
			BillType:   billType,
			Amount:     amount,
			CreateTime: time.Now().UnixNano(),
		},
	}
	return &billSwap
}

func NewBillAsset(uid, base, quote, operateId string, _billType int, _amount decimal.Decimal) *BillAssetSync {
	billSwap := BillAssetSync{
		BillAsset: entity.BillAsset{
			// BillID:       util.GenerateId(), // TODO snowflakeID
			UID:        uid,
			RefID:      operateId,
			Symbol:     util.ContractCode(base, quote),
			Currency:   strings.ToUpper(quote),
			BillType:   _billType,
			Amount:     _amount,
			CreateTime: time.Now().UnixNano(),
		},
	}
	return &billSwap
}
