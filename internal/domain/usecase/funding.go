package usecase

import (
	"context"
	"sync"

	"futures-asset/internal/domain/entity"
	"futures-asset/internal/domain/repository"
)

type FundingUseCase interface {
	FundingFeeList(ctx context.Context, req *repository.FundingFeeListParam) (repository.FundingFeeList, error)

	GetFundRateList(ctx context.Context, req *repository.FundRateParam) (repository.FundRateReply, error)
	GetFundRate(ctx context.Context, contractCode string) (repository.FundingRate, error)
	FundRateAll(ctx context.Context) (repository.FundingRateAll, error)
	LastFundMap(ctx context.Context, wg *sync.WaitGroup) (r map[string]entity.LogFundingRate)
	AllFundData(ctx context.Context, wg *sync.WaitGroup) (r []repository.FundingRate)

	GetBaseNum(ctx context.Context, key string) int
	SetBaseNum(ctx context.Context, key string, value int) error
}
