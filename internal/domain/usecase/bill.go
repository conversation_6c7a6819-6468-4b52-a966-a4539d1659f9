package usecase

import (
	"context"

	"futures-asset/internal/libs/pager"
	"github.com/shopspring/decimal"
)

type BillUseCase interface {
	BillAsset(ctx context.Context, req *BillAssetParam) (*BillAssetReply, error)
}

type (
	BillAssetParam struct {
		pager.Condition
		Symbol    string `json:"symbol"`
		Currency  string `json:"currency"`   // 币种
		BillType  int    `json:"bill_type"`  // 类型
		StartTime int64  `json:"start_time"` // 开始时间
		EndTime   int64  `json:"end_time"`   // 结束时间
		UID       string `json:"uid"`        // 用户id
		Plat      int    `json:"plat"`       // plat 0.前端app,web或H5，1.财务部门 2.客服部门 3.运营部门
		IsTrial   int8   `json:"is_trial"`   // 是否有体验金 0.全部 1.有
	}

	BillAsset struct {
		BillID         int64           `json:"bill_id"`          // 账单ID
		UID            string          `json:"uid"`              // 用户ID
		Symbol         string          `json:"symbol"`           // 合约代码
		Currency       string          `json:"currency"`         // 资产币种
		BillType       int             `json:"bill_type"`        // 账单类型
		FlowType       int             `json:"flow_type"`        // 流水方向（1-入账，2-出账）
		Amount         decimal.Decimal `json:"amount"`           // 数量
		Balance        decimal.Decimal `json:"balance"`          // 变动后余额
		FundingRate    decimal.Decimal `json:"funding_rate"`     // 资金费率
		MarkPrice      decimal.Decimal `json:"mark_price"`       // 标记价格
		RefID          string          `json:"ref_id"`           // 关联业务单号 (结算的时候是结算的唯一id， 爆仓的时候是爆仓操作的唯一id)
		FromPair       string          `json:"from_pair"`        // 转出币对
		ToPair         string          `json:"to_pair"`          // 转入币对
		FromWalletType int             `json:"from_wallet_type"` // 转出账户
		ToWalletType   int             `json:"to_wallet_type"`   // 转入账户
		RecycleID      string          `json:"recycle_id"`       // 体验金回收的唯一id
		CreateTime     int64           `json:"create_time"`      // 创建时间
	}
	BillAssetReply struct {
		pager.Page
		List        []BillAsset     `json:"list"`
		TotalAmount decimal.Decimal `json:"total_amount"` // 穿仓补贴查询总计
	}
)
