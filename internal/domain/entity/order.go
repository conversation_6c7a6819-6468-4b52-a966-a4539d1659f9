package entity

import (
	"github.com/shopspring/decimal"
)

// 同engine服务model中的Order结构体
type Order struct {
	Symbol          string          `json:"symbol"`      // 合约代码(BTC-USD)
	MarginMode      int             `json:"margin_mode"` // 保证金模式
	OrderId         string          `json:"order_id"`    // 委托单ID
	ClientOid       string          `json:"client_oid"`  // 用户自编委托单ID
	Side            int             `json:"side"`        // 委托方向 buy-买 sell-卖
	PosSide         int             `json:"pos_side"`    // 多或空 1-多 2-空
	UID             string          // 用户ID
	OrderType       int             `json:"order_type"`       // 委托类型
	IsLimitOrder    int             `json:"is_limit_order"`   // 是否限价订单-1:是,2:否(市价)
	StopLossType    int             `json:"stop_loss_type"`   // 止盈止损类型-1:仓位止损,2:委托止损(开仓委托成交后挂单)
	ParentId        string          `json:"parent_id"`        // 委托单止盈止损的父籍orderId
	Price           decimal.Decimal `json:"price"`            // 委托价格
	Amount          decimal.Decimal `json:"amount"`           // 委托数量
	Money           decimal.Decimal `json:"money"`            // 委托金额
	FilledAmount    decimal.Decimal `json:"filled_amount"`    // 已成交数量
	FilledMoney     decimal.Decimal `json:"filled_money"`     // 已成交金额
	FilledFee       decimal.Decimal `json:"filled_fee"`       // 已成交手续费(买入为交易币，卖出为计价币)
	DeductFee       decimal.Decimal `json:"deduct_fee"`       // 已抵扣手续费
	FeeCurrency     string          `json:"fee_currency"`     // 手续费币种
	TriggerMode     int             `json:"trigger_mode"`     // 触发模式-1:按价格,2:按收益率,3:按收益额
	TriggerProfit   decimal.Decimal `json:"trigger_profit"`   // 按收益触发时,收益率或收益额
	TriggerPrice    decimal.Decimal `json:"trigger_price"`    // 委托单触发价
	TriggerType     string          `json:"trigger_type"`     // 委托单触发类型 gte大于等于,lte小于等于
	TriggerState    int             `json:"trigger_state"`    // 触发状态
	WorkingType     int             `json:"working_type"`     // 条件价格触发类型
	Leverage        int             `json:"leverage"`         // 杠杆倍数
	LiquidationType int             `json:"liquidation_type"` // 强平类型 0-非强平类型 1-多空轧差 2-部分接管 3-全部接管
	PositionMode    int             `json:"position_mode"`    // 持仓模式 1-单向持仓 2-双向持仓
	IsMixMargin     int             `json:"is_mix_margin"`    // 是否混合保证金模式
	TimeInForce     string          `json:"time_in_force"`    // 有效时间类型
	State           int             `json:"state"`            // 委托单状态 submitted-已提交(等待成交，已进入撮合队列), partial-filled-部分成交, partial-canceled-部分成交撤销, filled-完全成交, canceled-已撤销， created-已创建，canceling-撤单中(已被取消)
	AccountType     string          `json:"account_type"`     // 账户类型 spot-币币账户 otc-OTC账户 margin-逐仓杠杆账户 super-margin-全仓杠杆账户 swap-永续合约账户 futures-交割合约账户
	Platform        string          `json:"platform"`         // 平台来源
	CancelType      int             `json:"cancel_type"`      // 撤单类型(1用户、2系统、3运营)
	CloseType       int             `json:"close_type"`       // 平仓类型
	ProfitReal      decimal.Decimal `json:"profit_real"`      // 平仓已实现盈亏
	HaveTrial       int8            `json:"have_trial"`       // 是否有体验金
	AwardIds        []string        `json:"award_ids"`        // 奖励操作ID
	DeductTypes     []int           `json:"deduct_types"`     // 抵扣类型
	CreateTime      int64           `json:"create_time"`      // 创建时间
	UpdateTime      int64           `json:"update_time"`      // 状态更新时间
}
