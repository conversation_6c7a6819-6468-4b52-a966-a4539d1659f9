package usecase

import (
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"

	"futures-asset/configs"
	"futures-asset/internal/domain"
	"futures-asset/internal/domain/entity"
	"futures-asset/internal/domain/usecase"
	client "futures-asset/internal/libs/kafka"

	"github.com/IBM/sarama"
	"go.uber.org/dig"
	cfg "yt.com/backend/common.git/config"
	"yt.com/backend/common.git/kafka"
)

type ProducerUseCaseParam struct {
	dig.In

	Config  *cfg.Config[configs.Config] `name:"config"`
	Cluster *client.Cluster             `name:"kafka"`
}

type ProducerUseCase struct {
	cli     *kafka.Producer
	cluster *client.Cluster

	topic map[string]string
}

// NewProducerUseCase 建立kafka Producer
func NewProducerUseCase(param ProducerUseCaseParam) (usecase.ProducerUseCase, error) {
	producer, err := kafka.NewProducer(&kafka.Config{
		ClientID:          param.Config.Kafka.ClientID,
		Brokers:           param.Config.Kafka.Brokers,
		ChannelBufferSize: param.Config.Kafka.ChannelBufferSize,
		SASL: kafka.SASL{
			Enable:    param.Config.Kafka.SASL.Enable,
			Mechanism: sarama.SASLMechanism(param.Config.Kafka.SASL.Mechanism),
			User:      param.Config.Kafka.SASL.User,
			Password:  param.Config.Kafka.SASL.Password,
		},
	}, kafka.NewDefaultProducerConfig())
	if err != nil {
		return nil, fmt.Errorf("kafka.NewConsumerGroupUseCase error: %w", err)
	}

	var topics []string

	if name, ok := param.Config.Kafka.Producer.Topic[domain.AssetsBillTopic]; ok {
		topics = append(topics, name)
	}

	// if name, ok := param.Config.Kafka.Producer.Topic[domain.OptionBillTopic]; ok {
	// 	topics = append(topics, name)
	// }

	if name, ok := param.Config.Kafka.Producer.Topic[domain.FundingFeeTopic]; ok {
		topics = append(topics, name)
	}

	if name, ok := param.Config.Kafka.Producer.Topic[domain.FundingRateTopic]; ok {
		topics = append(topics, name)
	}

	// if name, ok := param.Config.Kafka.Producer.Topic[domain.TrialAssetTopic]; ok {
	// 	topics = append(topics, name)
	// }

	// if name, ok := param.Config.Kafka.Producer.Topic[domain.TrialAssetAddTopic]; ok {
	// 	topics = append(topics, name)
	// }

	// if name, ok := param.Config.Kafka.Producer.Topic[domain.TrialAssetRecycleTopic]; ok {
	// 	topics = append(topics, name)
	// }

	if err := param.Cluster.CreateTopics(param.Config.Kafka.Brokers, topics); err != nil {
		return nil, err
	}

	mTopics := make(map[string]string)
	for name, topic := range param.Config.Kafka.Producer.Topic {
		mTopics[name] = strings.Split(topic, ":")[0]
	}

	return &ProducerUseCase{
		cli:     producer,
		cluster: param.Cluster,
		topic:   mTopics,
	}, nil
}

type ProducerError struct {
	ID        string
	Partition int32
	Offset    int64
	Err       error
}

func (e ProducerError) Error() string {
	return fmt.Sprintf("send notify messages partition: %d offset: %d error: %s", e.Partition, e.Offset, e.Err.Error())
}

func (use *ProducerUseCase) SendAssetsBill(message []*entity.BillAsset) error {
	body, err := json.Marshal(message)
	if err != nil {
		return fmt.Errorf("SendAssetsBill json.Marshal error: %w", err)
	}

	partition, offset, err := use.cli.SendMessage(kafka.ProducerMessage{
		ID:    strconv.FormatInt(time.Now().UnixNano(), 10),
		Topic: use.topic[domain.AssetsBillTopic],
		Value: body,
	})
	if err != nil {
		return &ProducerError{
			Partition: partition,
			Offset:    offset,
			Err:       err,
		}
	}

	return nil
}

func (use *ProducerUseCase) SendFundingFee(message []*entity.LogFundingFee) error {
	body, err := json.Marshal(message)
	if err != nil {
		return fmt.Errorf("SendFundingFee json.Marshal error: %w", err)
	}

	partition, offset, err := use.cli.SendMessage(kafka.ProducerMessage{
		ID:    strconv.FormatInt(time.Now().UnixNano(), 10),
		Topic: use.topic[domain.FundingFeeTopic],
		Value: body,
	})
	if err != nil {
		return &ProducerError{
			Partition: partition,
			Offset:    offset,
			Err:       err,
		}
	}

	return nil
}

func (use *ProducerUseCase) SendFundingRate(message []*entity.LogFundingRate) error {
	body, err := json.Marshal(message)
	if err != nil {
		return fmt.Errorf("SendFundingRate json.Marshal error: %w", err)
	}

	partition, offset, err := use.cli.SendMessage(kafka.ProducerMessage{
		ID:    strconv.FormatInt(time.Now().UnixNano(), 10),
		Topic: use.topic[domain.FundingRateTopic],
		Value: body,
	})
	if err != nil {
		return &ProducerError{
			Partition: partition,
			Offset:    offset,
			Err:       err,
		}
	}

	return nil
}
