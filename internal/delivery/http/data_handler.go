package http

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"strings"

	"futures-asset/internal/delivery/http/payload"
	"futures-asset/internal/delivery/http/request"
	"futures-asset/internal/delivery/http/response"
	"futures-asset/internal/domain"
	"futures-asset/internal/domain/repository"
	"futures-asset/internal/domain/usecase"
	"futures-asset/internal/libs/pager"
	"futures-asset/pkg/eslib"
	"futures-asset/pkg/match"

	"github.com/gin-gonic/gin"
	"github.com/shopspring/decimal"
	"github.com/sirupsen/logrus"
	"go.uber.org/dig"
)

type DataHandlerParam struct {
	dig.In

	TrialUseCase      usecase.TrialUseCase
	FundingUseCase    usecase.FundingUseCase
	ProfitLossUseCase usecase.ProfitLossUseCase
	PositionUseCase   usecase.PositionUseCase
	PositionRepo      repository.PositionRepository
	BurstUseCase      usecase.BurstUseCase
	UserUseCase       usecase.UserUseCase
	PriceRepo         repository.PriceRepository
}

type dataHandler struct {
	trialUseCase      usecase.TrialUseCase
	fundingUseCase    usecase.FundingUseCase
	profitLossUseCase usecase.ProfitLossUseCase
	positionUseCase   usecase.PositionUseCase
	positionRepo      repository.PositionRepository
	burstUseCase      usecase.BurstUseCase
	userUseCase       usecase.UserUseCase
	priceRepo         repository.PriceRepository
}

func newDataHandler(param DataHandlerParam) *dataHandler {
	return &dataHandler{
		trialUseCase:      param.TrialUseCase,
		fundingUseCase:    param.FundingUseCase,
		profitLossUseCase: param.ProfitLossUseCase,
		positionUseCase:   param.PositionUseCase,
		positionRepo:      param.PositionRepo,
		burstUseCase:      param.BurstUseCase,
		userUseCase:       param.UserUseCase,
		priceRepo:         param.PriceRepo,
	}
}

func (handler *dataHandler) ProfitLossRecord(c *gin.Context) {
	var req payload.ReqPLRecord
	if err := request.ShouldBindQuery(c, &req); err != nil {
		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, err))
		return
	}
	code := req.CheckProcessParam()
	if code != http.StatusOK {
		logrus.Error(fmt.Sprintf("check param err.code:%+v, param:%+v", code, req))
		c.JSON(http.StatusBadRequest, response.NewError(code, errors.New(domain.ErrMsg[code])))
		return
	}

	data, err := handler.profitLossUseCase.GetPLRecord(c.Request.Context(), req.ToUseCase())
	if err != nil {
		logrus.Error(fmt.Sprintf("GetPLRecord err.code:%+v, param:%+v", code, req))
		c.JSON(http.StatusBadRequest, response.NewError(code, err))
		return
	}
	c.JSON(http.StatusOK, response.NewSuccess(data))
}

func (handler *dataHandler) LatestFundRate(c *gin.Context) {
	var req payload.ReqGetFundRate
	if err := request.ShouldBindQuery(c, &req); err != nil {
		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, err))
		return
	}

	fundRateRes, err := handler.fundingUseCase.GetFundRate(c.Request.Context(), strings.ToUpper(req.Symbol))
	if err != nil {
		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, err))
		return
	}

	c.JSON(http.StatusOK, response.NewSuccess(fundRateRes))
}

// func (handler *dataHandler) UserWinRate(c *gin.Context) {
// 	var req payload.WinRateParam
// 	if err := request.ShouldBindQuery(c, &req); err != nil {
// 		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, err))
// 		return
// 	}
// 	if len(req.ContractCode) == 0 {
// 		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, errors.New("contract code is empty")))
// 		return
// 	}

// 	c.JSON(http.StatusOK, response.NewSuccess(memorycache.GetCachedWinRateByCode(req.ContractCode)))
// }

func (handler *dataHandler) GetTotalSubsidy(c *gin.Context) {
	data, err := handler.profitLossUseCase.GetTotalSubsidy(c.Request.Context())
	if err != nil {
		logrus.Error(fmt.Sprintf("GetMarkPrice err:%s", err))
		c.JSON(http.StatusBadRequest, response.NewError(domain.Code251113, errors.New("get mark price err")))
		return
	}

	c.JSON(http.StatusOK, response.NewSuccess(data))
}

func (handler *dataHandler) FindSubsidy(c *gin.Context) {
	var req payload.SubsidyParam
	if err := request.ShouldBindQuery(c, &req); err != nil {
		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, err))
		return
	}
	result := payload.BillAssetReply{Page: pager.Page{PageIndex: req.PageIndex, PageSize: req.PageSize, Total: 0}, List: make([]payload.BillAsset, 0)}
	conditions := make(map[string]interface{})
	terms := make(map[string][]interface{})
	if len(req.UID) > 0 {
		conditions["uid"] = req.UID
	}
	if len(req.Currency) > 0 {
		conditions["currency"] = req.Currency
	}
	if len(req.Symbol) > 0 {
		conditions["symbol"] = req.Symbol
	}

	terms["billType"] = []interface{}{domain.BillTypeSubsidy, domain.BillTypeCloseSubsidy}
	ranges := make(map[string]map[string]interface{})
	if req.StartTime > 0 {
		if _, ok := ranges["operateTime"]; !ok {
			ranges["operateTime"] = make(map[string]interface{})
		}
		ranges["operateTime"]["gte"] = req.StartTime * 1e9
	}
	if req.EndTime > 0 {
		if _, ok := ranges["operateTime"]; !ok {
			ranges["operateTime"] = make(map[string]interface{})
		}
		ranges["operateTime"]["lte"] = req.EndTime * 1e9
	}

	if req.StartAmount.IsPositive() {
		if _, ok := ranges["amount"]; !ok {
			ranges["amount"] = make(map[string]interface{})
		}
		ranges["amount"]["gte"] = req.StartAmount
	}
	if req.EndAmount.IsPositive() {
		if _, ok := ranges["amount"]; !ok {
			ranges["amount"] = make(map[string]interface{})
		}
		ranges["amount"]["lte"] = req.EndAmount
	}
	assetBill := eslib.New(domain.EsBillIndex).SetFrom((req.PageIndex - 1) * req.PageSize).SetSize(req.PageSize).SetConditions(conditions).
		SetTerms(terms).
		SetRanges(ranges).
		SetSumAmount().
		SetSortFields(
			map[string]bool{
				"operateTime": false,
			})

	searchResult, err := assetBill.Search(context.Background())
	if err != nil || searchResult == nil {
		logrus.Error(fmt.Sprintf("es search err:%+v, searchResult:%+v", err, searchResult))
		c.JSON(http.StatusInternalServerError, response.NewError(domain.ErrEsSearch, errors.New(fmt.Sprintf("es search err:%+v, searchResult:%+v", err, searchResult))))
		return
	}

	sum, _ := searchResult.Aggregations.Sum("sumAmount")
	result.TotalAmount = decimal.NewFromFloat(*sum.Value)
	// 查询总量
	if searchResult.TotalHits() > 0 {
		result.Total = searchResult.TotalHits()
		for _, hit := range searchResult.Hits.Hits {
			itemByte, err := hit.Source.MarshalJSON()
			if err != nil {
				result.Total = result.Total - 1
				continue
			}
			var item payload.BillAsset
			err = json.Unmarshal(itemByte, &item)
			if err != nil {
				result.Total = result.Total - 1
				continue
			}
			item.CreateTime = item.CreateTime / 1e9
			result.List = append(result.List, item)
		}
	}

	c.JSON(http.StatusOK, response.NewSuccess(result))
}

func (handler *dataHandler) GetBaseNum(c *gin.Context) {
	data := payload.BaseNum{
		UBaseNum: handler.fundingUseCase.GetBaseNum(c.Request.Context(), domain.UBaseNum),
		BBaseNum: handler.fundingUseCase.GetBaseNum(c.Request.Context(), domain.BBaseNum),
	}

	c.JSON(http.StatusOK, response.NewSuccess(data))
}

func (handler *dataHandler) SetBaseNum(c *gin.Context) {
	var req payload.BaseNum
	if err := request.ShouldBindQuery(c, &req); err != nil {
		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, err))
		return
	}
	if req.UBaseNum == 0 || req.BBaseNum == 0 {
		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, errors.New(fmt.Sprintf("param err: %+v", req))))
		return
	}

	if err := handler.fundingUseCase.SetBaseNum(c.Request.Context(), domain.UBaseNum, req.UBaseNum); err != nil {
		c.JSON(http.StatusInternalServerError, response.NewError(domain.RedisUpdateErr, errors.New(fmt.Sprintf("update err:%+v, p:%+v", err, req))))
		return
	}
	if err := handler.fundingUseCase.SetBaseNum(c.Request.Context(), domain.BBaseNum, req.BBaseNum); err != nil {
		c.JSON(http.StatusInternalServerError, response.NewError(domain.RedisUpdateErr, errors.New(fmt.Sprintf("update err:%+v, p:%+v", err, req))))
		return
	}

	c.JSON(http.StatusOK, response.NewSuccess(""))
}

func (handler *dataHandler) UserStatistics(c *gin.Context) {
	var req payload.UserStatistics
	if err := request.ShouldBindQuery(c, &req); err != nil {
		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, err))
		return
	}

	if len(req.AccountType) == 0 {
		req.AccountType = match.AccountTypeSwap
	}

	reply, err := handler.userUseCase.GetUserStatistics(c.Request.Context(), req.ToUseCase())
	if err != nil {
		c.JSON(http.StatusInternalServerError, response.NewError(domain.InternalError, err))
		return
	}

	c.JSON(http.StatusOK, response.NewSuccess(reply))
}

// GetPlatData 获取平台当前仓位汇总信息
func (handler *dataHandler) GetPlatData(c *gin.Context) {
	var req payload.PlatDataReq
	if err := request.ShouldBindQuery(c, &req); err != nil {
		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, err))
		return
	}
	req.Symbol = strings.ToUpper(req.Symbol)
	res := payload.PlatDataRes{}
	fundRateRes, err := handler.fundingUseCase.GetFundRate(c.Request.Context(), req.Symbol)
	if err != nil {
		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, err))

		return
	}
	res.Symbol = fundRateRes.Symbol
	res.FundRate = fundRateRes.FundingRate
	res.ServerTime = fundRateRes.StartTime
	res.SettleTime = fundRateRes.EndTime
	res.IndexPrice = handler.priceRepo.GetIndexPrice(c.Request.Context(), req.Symbol)
	res.OpenPos = handler.positionUseCase.PosTotal(c.Request.Context(), req.Symbol)

	c.JSON(http.StatusOK, response.NewSuccess(res))
}

func (handler *dataHandler) UserOpenCloseTimes(c *gin.Context) {
	var req payload.OpenCloseTimesReq
	if err := request.ShouldBindQuery(c, &req); err != nil {
		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, err))
		return
	}

	res, err := handler.userUseCase.GetUserOpenCloseTimes(c.Request.Context(), req.ToUseCase())
	if err != nil {
		c.JSON(http.StatusInternalServerError, response.NewError(domain.InternalError, err))
		return
	}

	c.JSON(http.StatusOK, response.NewSuccess(res))
}
