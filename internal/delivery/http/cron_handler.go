package http

import (
	"errors"
	"net/http"
	"time"

	"futures-asset/internal/delivery/http/response"
	"futures-asset/internal/domain"
	"futures-asset/internal/domain/repository"

	"github.com/gin-gonic/gin"
	"github.com/go-redsync/redsync/v4"
	"github.com/sirupsen/logrus"
	"go.uber.org/dig"
)

type CronParam struct {
	dig.In

	RS *redsync.Redsync `name:"rs"`

	SettingRepository repository.SettingRepository
}

type cronHandler struct {
	rs *redsync.Redsync

	settingRepo repository.SettingRepository
}

func newCronHandler(param CronParam) *cronHandler {
	return &cronHandler{
		rs: param.RS,

		settingRepo: param.SettingRepository,
	}
}

func (handler *cronHandler) transactionHour(c *gin.Context) {
	// var req payload.TransactionHourReport
	// if err := request.ShouldBindJSON(c, &req); err != nil {
	// 	c.J<PERSON>(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, err))

	// 	return
	// }

	// if req.Time.IsZero() {
	// 	req.Time = time.Now()
	// }

	// logrus.Infof("start transactionCronUseCase.Hour")

	// err := handler.transactionCronUseCase.Hour(c.Request.Context(), req.Time)
	// if err != nil {
	// 	logrus.WithFields(logrus.Fields{
	// 		"err": err,
	// 	}).Error("transactionCronUseCase.Hour")

	// 	c.JSON(response.ServiceErrorToErrorResp(err))

	// 	return
	// }

	// logrus.Infof("end transactionCronUseCase.Hour")
	c.JSON(http.StatusOK, response.NewSuccess(gin.H{}))
}

func (handler *cronHandler) generateFundingRate(c *gin.Context) {
	fundRateMutex := handler.rs.NewMutex(domain.MutexSwapCronFund, redsync.WithExpiry(30*time.Second))
	if fundRateMutex.Lock() != nil {
		c.JSON(response.ServiceErrorToErrorResp(errors.New("get funding rate lock err")))

		return
	}
	defer fundRateMutex.Unlock()

	contractSettings, err := handler.settingRepo.GetAllPairSettingInfo(c.Request.Context())
	if err != nil {
		logrus.Error("makeFundRates get all contract config err.")

		return
	}

	for contractCode, contractConfig := range contractSettings {
		go makeFundRate(contractCode, &contractConfig)
	}

	c.JSON(http.StatusOK, response.NewSuccess(gin.H{}))
}

func makeFundRate(contractCode string, contractConfig *repository.ContractPair) {
	// indexPrice := sharedcache.IndexPrice(contractCode)
	// fundRateListKey := cachekey.GetFundRateRedisKey(contractCode)
	// swapcache.SetOneFundRate(fundRateListKey, contractCode, indexPrice, contractConfig.Interest)
	// _ = swapcache.PIndex(contractCode, contractConfig)
	// fundRate := swapcache.FundingRate(contractCode, contractConfig)
	// nowTime := time.Now()
	//
	// go message.New("", message.AssetQueueIndex, mqlib.CommonAmqp).
	// 	TopicFundRate(message.SwapAccount, contractCode).
	// 	Push(map[string]interface{}{
	// 		"contractCode": contractCode,
	// 		"fundRate":     fundRate,
	// 		"currentTime":  time.Now().Unix(),
	// 	})
	//
	// // 每天 0，8，16 点结算
	// if nowTime.Hour()%8 == 0 {
	// 	if nowTime.Minute() == 0 {
	// 		logFundRate := swap.LogFundRateSwap{
	// 			Base:        contractConfig.Base,
	// 			Quote:       contractConfig.Quote,
	// 			FundingRate:    fundRate,
	// 			MarkPrice:   pCache.GetMarkPrice(contractCode),
	// 			IndexPrice:  indexPrice,
	// 			OperateTime: nowTime.Unix(),
	// 		}
	//
	// 		loglib.GetLog().Info(fmt.Sprintf("Settlement All.fundRate:%+v,pCache:%+v,indexPrice:%+v,indexPrice:%+v.time:%+v", fundRate, pCache, indexPrice, contractCode, nowTime.Unix()))
	// 		if !fundRate.IsZero() {
	// 			SettlementAll(fundRate, contractCode, contractConfig, pCache)
	// 		}
	//
	// 		err := logFundRate.Insert(nil)
	// 		fmt.Println("=========================", logFundRate.Base, logFundRate.Quote, "logFundRate.Insert", err)
	// 		if err != nil {
	// 			loglib.GetLog().Error(fmt.Sprintf("insert fund rate log err:%+v, logFundRate:%+v", err, logFundRate))
	// 		}
	// 	}
	// }
}

// // SettlementAll 资金费率为正 净多出资金费用  资金费用为负 净空 出资金费用
// func SettlementAll(fundRate decimal.Decimal, contractCode string, contractConfig setting.ContractPair, pCache *price.PCache) {
// 	usersKey := cachekey.GetAllUserPosKey(contractCode)
// 	users, err := redislib.Redis().HGetAll(usersKey)
// 	if err != nil {
// 		loglib.GetLog().Error("usersKey HGetAll", usersKey, "error:", err)
// 		return
// 	}
// 	loglib.GetLog().Info(fmt.Sprintf("Settlement All.contractConfig: %+v, fundRate: %+v", contractConfig, fundRate))
// 	// 出资金费率的用户
// 	fundCostOutUsers := make([]entity.SettlementPos, 0)
// 	// 收资金费率的用户
// 	fundCostInUsers := make([]entity.SettlementPos, 0)
//
// 	// totalInPos：收资金费用用户的总仓位  totalOutPos：出资金费用用户的总仓位
// 	totalOutPos := decimal.Zero
// 	totalInPos := decimal.Zero
// 	totalLongPos := decimal.Zero
// 	totalShortPos := decimal.Zero
// 	for _, userPos := range users {
// 		userPosSwap := entity.UserHoldPos{}
// 		err = json.Unmarshal([]byte(userPos), &userPosSwap)
// 		if err != nil {
// 			loglib.GetLog().Error(fmt.Sprintf("unmarshal err:%+v, userPos:%s", err, userPos))
// 			continue
// 		}
// 		if userPosSwap.LongPos.IsZero() && userPosSwap.ShortPos.IsZero() && userPosSwap.BothPos.IsZero() {
// 			continue
// 		}
// 		checkBurst := cachelock.BurstLockParams{
// 			ContractCode: userPosSwap.ContractCode,
// 			Liquidation: cache.LiquidationInfo{
// 				UserId:     userPosSwap.UserId,
// 				MarginMode: enum.MarginMode(userPosSwap.MarginMode),
// 			},
// 		}
// 		if swapcache.UserBursting(checkBurst) {
// 			loglib.GetLog().Info(fmt.Sprintf("user in burst.userPos:%+v", userPosSwap))
// 			continue
// 		}
//
// 		base, quote := util.BaseQuote(userPosSwap.ContractCode)
// 		settlementPos := entity.SettlementPos{
// 			UserHoldPos: userPosSwap,
// 			Base:        base,
// 			Quote:       quote,
// 		}
//
// 		// 单向持仓需要特殊处理
// 		if !userPosSwap.BothPos.IsZero() && userPosSwap.BothTrialMargin.LessThanOrEqual(decimal.Zero) {
// 			settlementPos.PosSide = enum.BothPos
// 			settlementPos.Pos = userPosSwap.BothPos.Abs()
// 			if userPosSwap.BothPos.IsPositive() {
// 				totalLongPos = totalLongPos.Add(userPosSwap.BothPos.Abs())
// 				processLongPos(&fundCostOutUsers, &fundCostInUsers, &totalOutPos, &totalInPos, fundRate, &settlementPos)
// 			} else if userPosSwap.BothPos.IsNegative() {
// 				totalShortPos = totalShortPos.Add(userPosSwap.BothPos.Abs())
// 				processShortPos(&fundCostOutUsers, &fundCostInUsers, &totalOutPos, &totalInPos, fundRate, &settlementPos)
// 			}
// 		} else {
// 			// Isolated true 是逐仓 逐仓和全仓处理方式不一样
// 			if enum.MarginMode(userPosSwap.MarginMode) == enum.MarginModeIsolated {
// 				if userPosSwap.LongPos.IsPositive() && userPosSwap.LongTrialMargin.LessThanOrEqual(decimal.Zero) {
// 					settlementPos.PosSide = enum.LongPos
// 					settlementPos.Pos = userPosSwap.LongPos
// 					totalLongPos = totalLongPos.Add(userPosSwap.LongPos.Abs())
// 					processLongPos(&fundCostOutUsers, &fundCostInUsers, &totalOutPos, &totalInPos, fundRate, &settlementPos)
// 				}
// 				if userPosSwap.ShortPos.IsPositive() && userPosSwap.ShortTrialMargin.LessThanOrEqual(decimal.Zero) {
// 					settlementPos.PosSide = enum.ShortPos
// 					settlementPos.Pos = userPosSwap.ShortPos
// 					totalShortPos = totalShortPos.Add(userPosSwap.ShortPos.Abs())
// 					processShortPos(&fundCostOutUsers, &fundCostInUsers, &totalOutPos, &totalInPos, fundRate, &settlementPos)
// 				}
// 			} else {
// 				realPos := decimal.Zero
// 				longPos := decimal.Zero
// 				shortPos := decimal.Zero
// 				if userPosSwap.LongTrialMargin.LessThanOrEqual(decimal.Zero) {
// 					longPos = userPosSwap.LongPos
// 				}
// 				if userPosSwap.ShortTrialMargin.LessThanOrEqual(decimal.Zero) {
// 					shortPos = userPosSwap.ShortPos
// 				}
// 				realPos = longPos.Sub(shortPos)
// 				settlementPos.Pos = realPos.Abs()
// 				if realPos.IsPositive() {
// 					settlementPos.PosSide = enum.LongPos
// 					totalLongPos = totalLongPos.Add(userPosSwap.LongPos.Abs())
// 					processLongPos(&fundCostOutUsers, &fundCostInUsers, &totalOutPos, &totalInPos, fundRate, &settlementPos)
// 				} else if realPos.IsNegative() {
// 					settlementPos.PosSide = enum.ShortPos
// 					totalShortPos = totalShortPos.Add(userPosSwap.ShortPos.Abs())
// 					processShortPos(&fundCostOutUsers, &fundCostInUsers, &totalOutPos, &totalInPos, fundRate, &settlementPos)
// 				}
// 			}
// 		}
// 	}
//
// 	// 体验金仓位计算
// 	trialFundCostOutUsers, trialFundCostInUsers, trialTotalOutPos, trialTotalInPos, trialTotalLongPos, trialTotalShortPos := settlementTrialPos(fundRate, contractCode, contractConfig)
// 	if len(trialFundCostOutUsers) > 0 {
// 		fundCostOutUsers = append(fundCostOutUsers, trialFundCostOutUsers...)
// 	}
// 	if len(trialFundCostInUsers) > 0 {
// 		fundCostInUsers = append(fundCostInUsers, trialFundCostInUsers...)
// 	}
// 	totalOutPos = totalOutPos.Add(trialTotalOutPos)
// 	totalInPos = totalInPos.Add(trialTotalInPos)
// 	totalLongPos = totalLongPos.Add(trialTotalLongPos)
// 	totalShortPos = totalShortPos.Add(trialTotalShortPos)
//
// 	if !totalInPos.Equal(totalOutPos) {
// 		loglib.GetLog().Info(fmt.Sprintf("%s pos static err.totalInPos != totalOutPos.totalInPos:%+v, totalOutPos:%+v", contractCode, totalInPos, totalOutPos))
// 		loglib.GetLog().Info(fmt.Sprintf("******* %s totalLongPos：%+v totalShortPos:%+v", contractCode, totalLongPos, totalShortPos))
// 		loglib.GetLog().Info(fmt.Sprintf("******* %s fundCostOutUsers:%+v", contractCode, fundCostOutUsers))
// 		loglib.GetLog().Info(fmt.Sprintf("******* %s fundCostInUsers:%+v", contractCode, fundCostInUsers))
// 	}
//
// 	db, err := sqllib.Db()
// 	if err != nil {
// 		loglib.GetLog().Error(fmt.Sprintf("get db err:%+v", err))
// 	}
// 	// 总共实收资金费用
// 	totalRealGet := decimal.Zero
// 	for _, userPos := range fundCostOutUsers {
// 		if userPos.IsTrial {
// 			// 体验金仓位收取资金费率
// 			totalRealGet = totalRealGet.Add(PtGetTrialFundCost(&userPos, fundRate, pCache, db))
// 		} else {
// 			// 真实仓位收取资金费率
// 			totalRealGet = totalRealGet.Add(PtGetFundCost(&userPos, fundRate, pCache, db))
// 		}
// 	}
//
// 	loglib.GetLog().Info(fmt.Sprintf("Pt put fund cost.totalRealGet: %+v,totalInPos: %+v", totalRealGet, totalInPos))
// 	for _, userPos := range fundCostInUsers {
// 		if userPos.IsTrial {
// 			// 体验金仓位发放资金费率
// 			PtPutTrialFundCost(&userPos, fundRate, totalRealGet, totalInPos, pCache, db)
// 		} else {
// 			// 真实仓位收发放金费率
// 			PtPutFundCost(&userPos, fundRate, totalRealGet, totalInPos, pCache, db)
// 		}
// 	}
// }
//
// func settlementTrialPos(fundRate decimal.Decimal, contractCode string, contractConfig setting.ContractPair) ([]entity.SettlementPos, []entity.SettlementPos, decimal.Decimal, decimal.Decimal, decimal.Decimal, decimal.Decimal) {
// 	// 出资金费率的用户
// 	fundCostOutUsers := make([]entity.SettlementPos, 0)
// 	// 收资金费率的用户
// 	fundCostInUsers := make([]entity.SettlementPos, 0)
//
// 	// totalInPos：收资金费用用户的总仓位  totalOutPos：出资金费用用户的总仓位
// 	totalOutPos := decimal.Zero
// 	totalInPos := decimal.Zero
// 	totalLongPos := decimal.Zero
// 	totalShortPos := decimal.Zero
//
// 	usersKey := cachekey.GetAllUserTrialPosKey(contractCode)
// 	users, err := redislib.Redis().HGetAll(usersKey)
// 	if err != nil {
// 		loglib.GetLog().Error("userTrialKey HGetAll", usersKey, "error:", err)
// 		return fundCostOutUsers, fundCostInUsers, totalOutPos, totalInPos, totalLongPos, totalShortPos
// 	}
// 	loglib.GetLog().Info(fmt.Sprintf("Settlement All.contractConfig: %+v, fundRate: %+v", contractConfig, fundRate))
//
// 	for _, userPos := range users {
// 		userPosSwap := entity.UserHoldPos{}
// 		err = json.Unmarshal([]byte(userPos), &userPosSwap)
// 		if err != nil {
// 			loglib.GetLog().Error(fmt.Sprintf("unmarshal err:%+v, userPos:%s", err, userPos))
// 			continue
// 		}
// 		if userPosSwap.LongPos.IsZero() && userPosSwap.ShortPos.IsZero() && userPosSwap.BothPos.IsZero() {
// 			continue
// 		}
// 		checkBurst := cachelock.BurstLockParams{
// 			ContractCode: userPosSwap.ContractCode,
// 			Liquidation: cache.LiquidationInfo{
// 				UserId:     userPosSwap.UserId,
// 				MarginMode: enum.MarginMode(userPosSwap.MarginMode),
// 				IsTrialPos: true,
// 			},
// 		}
// 		if swapcache.UserBursting(checkBurst) {
// 			loglib.GetLog().Info(fmt.Sprintf("user in burst.userPos:%+v", userPosSwap))
// 			continue
// 		}
//
// 		base, quote := util.BaseQuote(userPosSwap.ContractCode)
// 		settlementPos := entity.SettlementPos{
// 			UserHoldPos: userPosSwap,
// 			Base:        base,
// 			Quote:       quote,
// 			IsTrial:     true,
// 		}
//
// 		// 因为全仓不能开保证金仓位,所直接略过
// 		// 单向持仓需要特殊处理
// 		if !userPosSwap.BothPos.IsZero() {
// 			settlementPos.PosSide = enum.BothPos
// 			settlementPos.Pos = userPosSwap.BothPos.Abs()
// 			if userPosSwap.BothPos.IsPositive() {
// 				totalLongPos = totalLongPos.Add(userPosSwap.BothPos.Abs())
// 				processLongPos(&fundCostOutUsers, &fundCostInUsers, &totalOutPos, &totalInPos, fundRate, &settlementPos)
// 			} else if userPosSwap.BothPos.IsNegative() {
// 				totalShortPos = totalShortPos.Add(userPosSwap.BothPos.Abs())
// 				processShortPos(&fundCostOutUsers, &fundCostInUsers, &totalOutPos, &totalInPos, fundRate, &settlementPos)
// 			}
// 		} else {
// 			if enum.MarginMode(userPosSwap.MarginMode) == enum.MarginModeIsolated {
// 				if userPosSwap.LongPos.IsPositive() {
// 					settlementPos.PosSide = enum.LongPos
// 					settlementPos.Pos = userPosSwap.LongPos
// 					totalLongPos = totalLongPos.Add(userPosSwap.LongPos.Abs())
// 					processLongPos(&fundCostOutUsers, &fundCostInUsers, &totalOutPos, &totalInPos, fundRate, &settlementPos)
// 				}
// 				if userPosSwap.ShortPos.IsPositive() {
// 					settlementPos.PosSide = enum.ShortPos
// 					settlementPos.Pos = userPosSwap.ShortPos
// 					totalShortPos = totalShortPos.Add(userPosSwap.ShortPos.Abs())
// 					processShortPos(&fundCostOutUsers, &fundCostInUsers, &totalOutPos, &totalInPos, fundRate, &settlementPos)
// 				}
// 			}
// 		}
// 	}
//
// 	return fundCostOutUsers, fundCostInUsers, totalOutPos, totalInPos, totalLongPos, totalShortPos
// }
//
// func processLongPos(fundCostOutUsers, fundCostInUsers *[]entity.SettlementPos, totalOutPos, totalInPos *decimal.Decimal, fundRate decimal.Decimal, settlementPos *entity.SettlementPos) {
// 	if fundRate.IsPositive() {
// 		*fundCostOutUsers = append(*fundCostOutUsers, *settlementPos)
// 		*totalOutPos = totalOutPos.Add(settlementPos.Pos)
// 	} else {
// 		*fundCostInUsers = append(*fundCostInUsers, *settlementPos)
// 		*totalInPos = totalInPos.Add(settlementPos.Pos)
// 	}
// }
//
// func processShortPos(fundCostOutUsers, fundCostInUsers *[]entity.SettlementPos, totalOutPos, totalInPos *decimal.Decimal, fundRate decimal.Decimal, settlementPos *entity.SettlementPos) {
// 	if fundRate.IsPositive() {
// 		*fundCostInUsers = append(*fundCostInUsers, *settlementPos)
// 		*totalInPos = totalInPos.Add(settlementPos.Pos)
// 	} else {
// 		*fundCostOutUsers = append(*fundCostOutUsers, *settlementPos)
// 		*totalOutPos = totalOutPos.Add(settlementPos.Pos)
// 	}
// }
//
// // PtGetFundCost 用户支付资金费用给平台, 资金费用取绝对值进位
// // 按照公式资金费用计算出来为负值，所以平台收的资金费用是公式计算出来的数值的绝对值并进位之后的结果
// func PtGetFundCost(tmpUserPos *entity.SettlementPos, fundRate decimal.Decimal, pCache *price.PCache, db *gorm.DB) decimal.Decimal {
// 	settleId := util.GenerateId()
// 	fundCost := tmpUserPos.Pos.Mul(pCache.GetMarkPrice(tmpUserPos.ContractCode)).Mul(fundRate).Abs()
// 	fundCost, _ = util.RoundCeil(fundCost, constvar.CurrencyPrecision)
// 	userMutex := redislib.NewMutex(redisconst.MutexSwapPosLock+tmpUserPos.UserId, 30*time.Second)
// 	if userMutex.Lock() != nil {
// 		loglib.GetLog().Info(fmt.Sprintf("PtGetFundCost lock err.tmpUserPos:%+v,fundRate:%+v,pCache:%+v", tmpUserPos, fundRate, pCache))
// 		return decimal.Zero
// 	}
// 	defer userMutex.Unlock()
//
// 	userCache := swapcache.NewPosCache(swapcache.CacheParam{
// 		TradeCommon: parameter.TradeCommon{
// 			Base:  tmpUserPos.Base,
// 			Quote: tmpUserPos.Quote,
// 		},
// 	}, tmpUserPos.UserId)
// 	userAsset, err := userCache.Load()
// 	if err != nil {
// 		loglib.GetLog().Info(fmt.Sprintf("PtGetFundCost get asset err.userCache:%+v,tmpUserPos:%+v,fundRate:%+v,pCache:%+v", userCache, tmpUserPos, fundRate, pCache))
// 		return decimal.Zero
// 	}
//
// 	bParamList := make([]parameter.BalanceUpdate, 0)
// 	bIsolatedParamList := make([]parameter.BalanceUpdate, 0)
// 	isTrialPos := false
//
// 	if tmpUserPos.MarginMode == 0 {
// 		if tmpUserPos.IsTrial {
// 			switch tmpUserPos.PosSide {
// 			case enum.LongPos:
// 				tmpUserPos.MarginMode = userAsset.TrialLongPos.MarginMode
// 			case enum.ShortPos:
// 				tmpUserPos.MarginMode = userAsset.TrialShortPos.MarginMode
// 			case enum.BothPos:
// 				tmpUserPos.MarginMode = userAsset.TrialBothPos.MarginMode
// 			default:
// 				return decimal.Zero
// 			}
// 		} else {
// 			switch tmpUserPos.PosSide {
// 			case enum.LongPos:
// 				tmpUserPos.MarginMode = userAsset.LongPos.MarginMode
// 				isTrialPos = userAsset.LongPos.TrialMargin.GreaterThan(decimal.Zero)
// 			case enum.ShortPos:
// 				tmpUserPos.MarginMode = userAsset.ShortPos.MarginMode
// 				isTrialPos = userAsset.ShortPos.TrialMargin.GreaterThan(decimal.Zero)
// 			case enum.BothPos:
// 				tmpUserPos.MarginMode = userAsset.BothPos.MarginMode
// 				isTrialPos = userAsset.BothPos.TrialMargin.GreaterThan(decimal.Zero)
// 			default:
// 				return decimal.Zero
// 			}
// 		}
// 	}
//
// 	if isTrialPos {
// 		return decimal.Zero
// 	}
//
// 	switch enum.MarginMode(tmpUserPos.MarginMode) {
// 	case enum.MarginModeCross:
// 		// 获取quote 总资产折合 假如qBalance < fundCost, fundCost修改
// 		rate := decimal.NewFromInt(1)
// 		qBalance := userAsset.CBalance(tmpUserPos.Quote)
// 		holdMargin, _, _, _, err := userCache.TotalCrossMaintainMargin(pCache, tmpUserPos.ContractCode)
// 		if err != nil {
// 			loglib.GetLog().Info(fmt.Sprintf("PtGetFundCost TotalCrossMaintainMargin error: %v", err))
// 			return decimal.Zero
// 		}
// 		if userAsset.JoinMargin == enum.JoinMargin {
// 			rate = pCache.SpotRate(tmpUserPos.Quote, constvar.SwapUsdt)
// 			if rate.LessThanOrEqual(decimal.Zero) {
// 				loglib.GetLog().Info(fmt.Sprintln("PtGetFundCost rate error:", rate))
// 				return decimal.Zero
// 			}
// 			uBalance, err := userAsset.TotalJoinBalance(pCache)
// 			if err != nil {
// 				loglib.GetLog().Info(fmt.Sprintf("PtGetFundCost TotalBalance err.userCache:%+v,tmpUserPos:%+v,fundRate:%+v,pCache:%+v, err:%v", userCache, tmpUserPos, fundRate, pCache, err))
// 				return decimal.Zero
// 			}
// 			uCost := fundCost.Mul(rate)
// 			if uBalance.GreaterThan(uCost.Add(holdMargin)) {
// 				totalCollect := uCost
// 				bParam := userCache.FundingBalanceParam(settleId, constvar.SwapUsdt, totalCollect.Neg())
// 				bParamList = append(bParamList, bParam)
// 			}
//
// 		} else {
// 			if qBalance.GreaterThan(fundCost.Add(holdMargin)) {
// 				bParam := userCache.FundingBalanceParam(settleId, userCache.Quote, fundCost.Neg())
// 				bParamList = append(bParamList, bParam)
// 			}
// 		}
//
// 	case enum.MarginModeIsolated:
// 		bParam := userCache.FundingBalanceParam(settleId, userCache.Quote, fundCost.Neg())
// 		switch tmpUserPos.PosSide {
// 		case enum.LongPos:
// 			posValue := userAsset.LongPos.CalcPosValue(pCache)
// 			marginLevel, _, err := setting.FetchMarginLevel(tmpUserPos.Base, tmpUserPos.Quote, posValue)
// 			if err != nil {
// 				logger.Error(0, userAsset.LongPos.ContractCode, userAsset.LongPos.UserId, "PtGetFundCost FetchMarginLevel error:", err)
// 				return decimal.Zero
// 			}
//
// 			holdMargin := marginLevel.HoldingMarginRate.Mul(posValue)
// 			if userAsset.LongPos.IsolatedMargin.GreaterThan(fundCost.Add(holdMargin)) {
// 				log.Println("PtGetFundCost", userAsset.UserId, "cost", fundCost)
// 				userAsset.LongPos.IsolatedMargin = userAsset.LongPos.IsolatedMargin.Sub(fundCost)
// 				if userAsset.LongPos.TrialMargin.GreaterThan(decimal.Zero) {
// 					userAsset.LongPos.TrialMargin = userAsset.LongPos.TrialMargin.Sub(fundCost)
// 				}
// 				bIsolatedParamList = append(bIsolatedParamList, bParam)
// 			}
//
// 			// if userAsset.LongPos.IsolatedMargin.LessThan(fundCost) {
// 			//	fundCost = userAsset.LongPos.IsolatedMargin
// 			// }
// 			// userAsset.LongPos.IsolatedMargin = userAsset.LongPos.IsolatedMargin.Sub(fundCost)
//
// 		case enum.ShortPos:
// 			posValue := userAsset.ShortPos.CalcPosValue(pCache)
// 			marginLevel, _, err := setting.FetchMarginLevel(tmpUserPos.Base, tmpUserPos.Quote, posValue)
// 			if err != nil {
// 				logger.Error(0, userAsset.ShortPos.ContractCode, userAsset.ShortPos.UserId, "PtGetFundCost FetchMarginLevel error:", err)
// 				return decimal.Zero
// 			}
//
// 			holdMargin := marginLevel.HoldingMarginRate.Mul(posValue)
// 			if userAsset.ShortPos.IsolatedMargin.GreaterThan(fundCost.Add(holdMargin)) {
// 				log.Println("PtGetFundCost", userAsset.UserId, "cost", fundCost)
// 				userAsset.ShortPos.IsolatedMargin = userAsset.ShortPos.IsolatedMargin.Sub(fundCost)
// 				if userAsset.ShortPos.TrialMargin.GreaterThan(decimal.Zero) {
// 					userAsset.ShortPos.TrialMargin = userAsset.ShortPos.TrialMargin.Sub(fundCost)
// 				}
// 				bIsolatedParamList = append(bIsolatedParamList, bParam)
// 			}
// 		case enum.BothPos:
// 			posValue := userAsset.BothPos.CalcPosValue(pCache)
// 			marginLevel, _, err := setting.FetchMarginLevel(tmpUserPos.Base, tmpUserPos.Quote, posValue)
// 			if err != nil {
// 				logger.Error(0, userAsset.BothPos.ContractCode, userAsset.BothPos.UserId, "PtGetFundCost FetchMarginLevel error:", err)
// 				return decimal.Zero
// 			}
//
// 			holdMargin := marginLevel.HoldingMarginRate.Mul(posValue)
// 			if userAsset.BothPos.IsolatedMargin.GreaterThan(fundCost.Add(holdMargin)) {
// 				log.Println("PtGetFundCost", userAsset.UserId, "cost", fundCost)
// 				userAsset.BothPos.IsolatedMargin = userAsset.BothPos.IsolatedMargin.Sub(fundCost)
// 				if userAsset.BothPos.TrialMargin.GreaterThan(decimal.Zero) {
// 					userAsset.BothPos.TrialMargin = userAsset.BothPos.TrialMargin.Sub(fundCost)
// 				}
// 				bIsolatedParamList = append(bIsolatedParamList, bParam)
// 			}
// 		default:
// 			loglib.GetLog().Info(fmt.Sprintf("PtGetFundCost PosSide err.tmpUserPos:%+v,fundRate:%+v,pCache:%+v", tmpUserPos, fundRate, pCache))
// 			return decimal.Zero
//
// 		}
//
// 	default:
// 		loglib.GetLog().Info(fmt.Sprintf("PtGetFundCost MarginMode err.tmpUserPos:%+v,fundRate:%+v", tmpUserPos, fundRate))
// 		return decimal.Zero
//
// 	}
//
// 	if len(bParamList) > 0 {
// 		for _, param := range bParamList {
// 			if !param.Amount.IsZero() {
// 				balanceRes := userCache.BalanceAdd(param, userAsset, pCache, true)
//
// 				for i := 0; i < len(balanceRes.BillAssetLogs); i++ {
// 					balanceRes.BillAssetLogs[i].MarkPrice = pCache.GetMarkPrice(tmpUserPos.ContractCode)
// 					balanceRes.BillAssetLogs[i].FundingRate = fundRate
// 				}
//
// 				err = userCache.UpdatePosAndAsset(userAsset)
// 				if err != nil {
// 					loglib.GetLog().Info(fmt.Sprintf("UpdatePosAndAsset err:%+v, tmpUserPos:%+v,fundRate:%+v,pCache:%+v,userAsset:%+v", err, tmpUserPos, fundRate, pCache, userAsset))
// 					return decimal.Zero
// 				}
// 				InsertLogFunding(tmpUserPos, *balanceRes.AssetLogs[0], userAsset, fundRate, pCache.GetMarkPrice(tmpUserPos.ContractCode), db)
// 				log.Println("PtGetFundCost InsertLogFunding", userAsset.UserId, fundRate)
// 				go func() {
// 					// 推送 资金费用 账单
// 					redis := redislib.Redis()
// 					coupling.AddAssetLogs(redis, balanceRes.AssetLogs...)
// 					coupling.AddBills(redis, balanceRes.BillAssetLogs...)
// 				}()
// 				// 账本统计
// 				go func(p parameter.BalanceUpdate) {
// 					balance, err := swapcache.GetCurrencyTotalBalance(userAsset.UserId, p.Currency)
// 					if err != nil {
// 						loglib.GetLog().Errorf("PtGetFundCost swapcache.GetCurrencyTotalBalance err: %v", err)
// 						return
// 					}
// 					if p.Amount.IsPositive() {
// 						es.SaveLedgerDetail(userAsset.UserId, p.Currency, es.DERIVATIVES_ACCOUNT_TYPE, es.FUND_FEE_LEDGER_TYPE, 0, p.Amount.Neg(), balance)
// 					} else {
// 						es.SaveLedgerDetail(userAsset.UserId, p.Currency, es.DERIVATIVES_ACCOUNT_TYPE, es.FUND_FEE_LEDGER_TYPE, 0, p.Amount, balance)
// 					}
// 				}(param)
// 			}
// 		}
// 	}
//
// 	if len(bIsolatedParamList) > 0 {
// 		for _, param := range bIsolatedParamList {
// 			if !param.Amount.IsZero() {
// 				balanceRes := parameter.BalanceRes{
// 					AssetLogs:     make([]*entity.MqCmsAsset, 0),
// 					BillAssetLogs: make([]entity.BillAssetSync, 0),
// 				}
// 				userCache.OnBalanceAdd(param, &balanceRes, param.Amount, param.Currency)
// 				for i := 0; i < len(balanceRes.BillAssetLogs); i++ {
// 					balanceRes.BillAssetLogs[i].MarkPrice = pCache.GetMarkPrice(tmpUserPos.ContractCode)
// 					balanceRes.BillAssetLogs[i].FundingRate = fundRate
// 				}
//
// 				err = userCache.UpdatePosAndAsset(userAsset)
// 				if err != nil {
// 					loglib.GetLog().Info(fmt.Sprintf("UpdatePosAndAsset err:%+v, tmpUserPos:%+v,fundRate:%+v,pCache:%+v,userAsset:%+v", err, tmpUserPos, fundRate, pCache, userAsset))
// 					return decimal.Zero
// 				}
// 				InsertLogFunding(tmpUserPos, *balanceRes.AssetLogs[0], userAsset, fundRate, pCache.GetMarkPrice(tmpUserPos.ContractCode), db)
//
// 				log.Println("PtGetFundCost InsertLogFunding", userAsset.UserId, fundRate)
//
// 				go func() {
// 					// 推送 资金费用 账单
// 					redis := redislib.Redis()
// 					coupling.AddAssetLogs(redis, balanceRes.AssetLogs...)
// 					coupling.AddBills(redis, balanceRes.BillAssetLogs...)
// 				}()
// 				// 账本统计
// 				go func(p parameter.BalanceUpdate) {
// 					balance, err := swapcache.GetCurrencyTotalBalance(userAsset.UserId, p.Currency)
// 					if err != nil {
// 						loglib.GetLog().Errorf("2 PtGetFundCost swapcache.GetCurrencyTotalBalance err: %v", err)
// 						return
// 					}
// 					if p.Amount.IsPositive() {
// 						es.SaveLedgerDetail(userAsset.UserId, p.Currency, es.DERIVATIVES_ACCOUNT_TYPE, es.FUND_FEE_LEDGER_TYPE, 0, p.Amount.Neg(), balance)
// 					} else {
// 						es.SaveLedgerDetail(userAsset.UserId, p.Currency, es.DERIVATIVES_ACCOUNT_TYPE, es.FUND_FEE_LEDGER_TYPE, 0, p.Amount, balance)
// 					}
// 				}(param)
// 			}
// 		}
// 	}
//
// 	return fundCost
// }
//
// // PtPutFundCost 平台支付资金费用给用户 资金费用取绝对值截取
// func PtPutFundCost(tmpUserPos *entity.SettlementPos, fundRate, totalRealGet, totalInPos decimal.Decimal, pCache *price.PCache, db *gorm.DB) decimal.Decimal {
// 	settleId := util.GenerateId()
// 	fundCost := tmpUserPos.Pos.Mul(pCache.GetMarkPrice(tmpUserPos.ContractCode)).Mul(fundRate).Abs().Truncate(constvar.CurrencyPrecision)
// 	if !totalInPos.IsZero() {
// 		receivableCost := tmpUserPos.Pos.Div(totalInPos).Mul(totalRealGet).Abs().Truncate(constvar.CurrencyPrecision)
// 		// 有可能实际收取的不够，所以需要按比例重新计算下
// 		if receivableCost.Cmp(fundCost) < 0 {
// 			loglib.GetLog().Info(fmt.Sprintf("fund rate in receiv < real.receivableCost:%+v,fundCost:%+v,tmpUserPos:%+v", receivableCost, fundCost, tmpUserPos))
// 			fundCost = receivableCost
// 		}
// 	}
// 	userMutex := redislib.NewMutex(redisconst.MutexSwapPosLock+tmpUserPos.UserId, 30*time.Second)
// 	if userMutex.Lock() != nil {
// 		loglib.GetLog().Info(fmt.Sprintf("PtPutFundCost lock err.tmpUserPos:%+v,fundRate:%+v,pCache:%+v", tmpUserPos, fundRate, pCache))
// 		return decimal.Zero
// 	}
// 	defer userMutex.Unlock()
//
// 	userCache := swapcache.NewPosCache(swapcache.CacheParam{
// 		TradeCommon: parameter.TradeCommon{
// 			Base:  tmpUserPos.Base,
// 			Quote: tmpUserPos.Quote,
// 		},
// 	}, tmpUserPos.UserId)
// 	userAsset, err := userCache.Load()
// 	if err != nil {
// 		loglib.GetLog().Info(fmt.Sprintf("PtPutFundCost get asset err.userCache:%+v,tmpUserPos:%+v,fundRate:%+v,pCache:%+v", userCache, tmpUserPos, fundRate, pCache))
// 		return decimal.Zero
// 	}
//
// 	isTrialPos := false
//
// 	switch tmpUserPos.PosSide {
// 	case enum.LongPos:
// 		isTrialPos = userAsset.LongPos.TrialMargin.GreaterThan(decimal.Zero)
//
// 	case enum.ShortPos:
// 		isTrialPos = userAsset.ShortPos.TrialMargin.GreaterThan(decimal.Zero)
//
// 	case enum.BothPos:
// 		isTrialPos = userAsset.BothPos.TrialMargin.GreaterThan(decimal.Zero)
//
// 	default:
// 		return decimal.Zero
//
// 	}
//
// 	if isTrialPos {
// 		return decimal.Zero
// 	}
//
// 	log.Println("PtPutFundCost", "UserId", userAsset.UserId, "settleId", settleId, "Quote", userCache.Quote, "fundCost", fundCost)
//
// 	bParamList := make([]parameter.BalanceUpdate, 0)
// 	bIsolatedParamList := make([]parameter.BalanceUpdate, 0)
// 	bParam := userCache.FundingBalanceParam(settleId, userCache.Quote, fundCost)
// 	if enum.MarginMode(tmpUserPos.MarginMode) == enum.MarginModeIsolated {
// 		switch tmpUserPos.PosSide {
// 		case enum.LongPos:
// 			userAsset.LongPos.IsolatedMargin = userAsset.LongPos.IsolatedMargin.Add(fundCost)
// 			if userAsset.LongPos.TrialMargin.GreaterThan(decimal.Zero) {
// 				userAsset.LongPos.TrialMargin = userAsset.LongPos.TrialMargin.Add(fundCost)
// 			}
// 			bIsolatedParamList = append(bIsolatedParamList, bParam)
//
// 		case enum.ShortPos:
// 			userAsset.ShortPos.IsolatedMargin = userAsset.ShortPos.IsolatedMargin.Add(fundCost)
// 			if userAsset.ShortPos.TrialMargin.GreaterThan(decimal.Zero) {
// 				userAsset.ShortPos.TrialMargin = userAsset.ShortPos.TrialMargin.Add(fundCost)
// 			}
// 			bIsolatedParamList = append(bIsolatedParamList, bParam)
//
// 		case enum.BothPos:
// 			userAsset.BothPos.IsolatedMargin = userAsset.BothPos.IsolatedMargin.Add(fundCost)
// 			if userAsset.BothPos.TrialMargin.GreaterThan(decimal.Zero) {
// 				userAsset.BothPos.TrialMargin = userAsset.BothPos.TrialMargin.Add(fundCost)
// 			}
// 			bIsolatedParamList = append(bIsolatedParamList, bParam)
//
// 		default:
// 			loglib.GetLog().Info(fmt.Sprintf("PtPutFundCost PosSide err.tmpUserPos:%+v,fundRate:%+v,pCache:%+v", tmpUserPos, fundRate, pCache))
// 			return decimal.Zero
//
// 		}
// 		// userAsset.Used = userAsset.Used.Add(fundCost)
// 	} else {
// 		bParamList = append(bParamList, bParam)
// 	}
//
// 	loglib.GetLog().Info(fmt.Sprintf("PtPutFundCost FundingBalanceParam %s fundCost: %+v", settleId, fundCost))
// 	if len(bParamList) > 0 {
// 		for _, param := range bParamList {
// 			if param.Amount.IsZero() {
// 				continue
// 			}
// 			balanceRes := userCache.BalanceAdd(param, userAsset, pCache, true)
// 			for i := 0; i < len(balanceRes.BillAssetLogs); i++ {
// 				balanceRes.BillAssetLogs[i].MarkPrice = pCache.GetMarkPrice(tmpUserPos.ContractCode)
// 				balanceRes.BillAssetLogs[i].FundingRate = fundRate
// 			}
// 			err = userCache.UpdatePosAndAsset(userAsset)
// 			if err != nil {
// 				loglib.GetLog().Info(fmt.Sprintf("UpdatePosAndAsset err:%+v, tmpUserPos:%+v,fundRate:%+v,pCache:%+v,userAsset:%+v", err, tmpUserPos, fundRate, pCache, userAsset))
// 				return decimal.Zero
// 			}
// 			if len(balanceRes.AssetLogs) > 0 {
// 				for _, assetLog := range balanceRes.AssetLogs {
// 					InsertLogFunding(tmpUserPos, *assetLog, userAsset, fundRate, pCache.GetMarkPrice(tmpUserPos.ContractCode), db)
// 					log.Println("PtPutFundCost InsertLogFunding", userAsset.UserId, fundRate)
// 				}
// 			}
// 			go func() {
// 				// 推送 资金费用 账单
// 				redis := redislib.Redis()
// 				coupling.AddAssetLogs(redis, balanceRes.AssetLogs...)
// 				coupling.AddBills(redis, balanceRes.BillAssetLogs...)
// 			}()
// 			// 账本统计
// 			go func(p parameter.BalanceUpdate) {
// 				balance, err := swapcache.GetCurrencyTotalBalance(userAsset.UserId, p.Currency)
// 				if err != nil {
// 					loglib.GetLog().Errorf("PtPutFundCost swapcache.GetCurrencyTotalBalance err: %v", err)
// 					return
// 				}
// 				if p.Amount.IsPositive() {
// 					es.SaveLedgerDetail(userAsset.UserId, p.Currency, es.DERIVATIVES_ACCOUNT_TYPE, es.FUND_FEE_LEDGER_TYPE, 0, p.Amount, balance)
// 				} else {
// 					es.SaveLedgerDetail(userAsset.UserId, p.Currency, es.DERIVATIVES_ACCOUNT_TYPE, es.FUND_FEE_LEDGER_TYPE, 0, p.Amount.Neg(), balance)
// 				}
// 			}(param)
// 		}
// 	}
// 	if len(bIsolatedParamList) > 0 {
// 		for _, param := range bIsolatedParamList {
// 			if param.Amount.IsZero() {
// 				continue
// 			}
// 			balanceRes := parameter.BalanceRes{
// 				AssetLogs:     make([]*entity.MqCmsAsset, 0),
// 				BillAssetLogs: make([]entity.BillAssetSync, 0),
// 			}
// 			userCache.OnBalanceAdd(param, &balanceRes, param.Amount, param.Currency)
// 			for i := 0; i < len(balanceRes.BillAssetLogs); i++ {
// 				balanceRes.BillAssetLogs[i].MarkPrice = pCache.GetMarkPrice(tmpUserPos.ContractCode)
// 				balanceRes.BillAssetLogs[i].FundingRate = fundRate
// 			}
// 			err = userCache.UpdatePosAndAsset(userAsset)
// 			if err != nil {
// 				loglib.GetLog().Info(fmt.Sprintf("UpdatePosAndAsset err:%+v, tmpUserPos:%+v,fundRate:%+v,pCache:%+v,userAsset:%+v", err, tmpUserPos, fundRate, pCache, userAsset))
// 				return decimal.Zero
// 			}
// 			if len(balanceRes.AssetLogs) > 0 {
// 				for _, assetLog := range balanceRes.AssetLogs {
// 					InsertLogFunding(tmpUserPos, *assetLog, userAsset, fundRate, pCache.GetMarkPrice(tmpUserPos.ContractCode), db)
// 					log.Println("PtPutFundCost InsertLogFunding", userAsset.UserId, fundRate)
// 				}
// 			}
// 			go func() {
// 				// 推送 资金费用 账单
// 				redis := redislib.Redis()
// 				coupling.AddAssetLogs(redis, balanceRes.AssetLogs...)
// 				coupling.AddBills(redis, balanceRes.BillAssetLogs...)
// 			}()
// 			go func(p parameter.BalanceUpdate) {
// 				balance, err := swapcache.GetCurrencyTotalBalance(userAsset.UserId, p.Currency)
// 				if err != nil {
// 					loglib.GetLog().Errorf("2 PtPutTrialFundCost swapcache.GetCurrencyTotalBalance err: %v", err)
// 					return
// 				}
// 				if p.Amount.IsPositive() {
// 					es.SaveLedgerDetail(userAsset.UserId, p.Currency, es.DERIVATIVES_ACCOUNT_TYPE, es.FUND_FEE_LEDGER_TYPE, 0, p.Amount, balance)
// 				} else {
// 					es.SaveLedgerDetail(userAsset.UserId, p.Currency, es.DERIVATIVES_ACCOUNT_TYPE, es.FUND_FEE_LEDGER_TYPE, 0, p.Amount.Neg(), balance)
// 				}
// 			}(param)
// 		}
// 	}
//
// 	return fundCost
// }
//
// // PtGetTrialFundCost 体验金用户支付资金费用给平台, 资金费用取绝对值进位
// // 按照公式资金费用计算出来为负值，所以平台收的资金费用是公式计算出来的数值的绝对值并进位之后的结果
// func PtGetTrialFundCost(tmpUserPos *entity.SettlementPos, fundRate decimal.Decimal, pCache *price.PCache, db *gorm.DB) decimal.Decimal {
// 	settleId := util.GenerateId()
// 	fundCost := tmpUserPos.Pos.Mul(pCache.GetMarkPrice(tmpUserPos.ContractCode)).Mul(fundRate).Abs()
// 	fundCost, _ = util.RoundCeil(fundCost, constvar.CurrencyPrecision)
// 	userMutex := redislib.NewMutex(redisconst.MutexSwapPosLock+tmpUserPos.UserId, 30*time.Second)
// 	if userMutex.Lock() != nil {
// 		loglib.GetLog().Info(fmt.Sprintf("PtGetTrialFundCost lock err.tmpUserPos:%+v,fundRate:%+v,pCache:%+v", tmpUserPos, fundRate, pCache))
// 		return decimal.Zero
// 	}
// 	defer userMutex.Unlock()
//
// 	userCache := swapcache.NewPosCache(swapcache.CacheParam{
// 		TradeCommon: parameter.TradeCommon{
// 			Base:  tmpUserPos.Base,
// 			Quote: tmpUserPos.Quote,
// 		},
// 	}, tmpUserPos.UserId)
// 	userAsset, err := userCache.Load()
// 	if err != nil {
// 		loglib.GetLog().Info(fmt.Sprintf("PtGetTrialFundCost get asset err.userCache:%+v,tmpUserPos:%+v,fundRate:%+v,pCache:%+v", userCache, tmpUserPos, fundRate, pCache))
// 		return decimal.Zero
// 	}
//
// 	bIsolatedParamList := make([]parameter.BalanceUpdate, 0)
//
// 	if tmpUserPos.MarginMode == 0 {
// 		switch tmpUserPos.PosSide {
// 		case enum.LongPos:
// 			tmpUserPos.MarginMode = userAsset.TrialLongPos.MarginMode
// 		case enum.ShortPos:
// 			tmpUserPos.MarginMode = userAsset.TrialShortPos.MarginMode
// 		case enum.BothPos:
// 			tmpUserPos.MarginMode = userAsset.TrialBothPos.MarginMode
// 		default:
// 			return decimal.Zero
// 		}
// 	}
//
// 	switch enum.MarginMode(tmpUserPos.MarginMode) {
// 	case enum.MarginModeIsolated:
// 		bParam := userCache.FundingBalanceParam(settleId, userCache.Quote, fundCost.Neg())
// 		switch tmpUserPos.PosSide {
// 		case enum.LongPos:
// 			posValue := userAsset.TrialLongPos.CalcPosValue(pCache)
// 			marginLevel, _, err := setting.FetchMarginLevel(tmpUserPos.Base, tmpUserPos.Quote, posValue)
// 			if err != nil {
// 				logger.Error(0, userAsset.TrialLongPos.ContractCode, userAsset.TrialLongPos.UserId, "PtGetTrialFundCost FetchMarginLevel error:", err)
// 				return decimal.Zero
// 			}
//
// 			holdMargin := marginLevel.HoldingMarginRate.Mul(posValue)
// 			if userAsset.TrialLongPos.IsolatedMargin.GreaterThan(fundCost.Add(holdMargin)) {
// 				log.Println("PtGetTrialFundCost", userAsset.UserId, "cost", fundCost)
// 				userAsset.TrialLongPos.IsolatedMargin = userAsset.TrialLongPos.IsolatedMargin.Sub(fundCost)
// 				if userAsset.TrialLongPos.TrialMargin.GreaterThan(decimal.Zero) {
// 					userAsset.TrialLongPos.TrialMargin = userAsset.TrialLongPos.TrialMargin.Sub(fundCost)
// 				}
// 				bIsolatedParamList = append(bIsolatedParamList, bParam)
// 			}
// 		case enum.ShortPos:
// 			posValue := userAsset.TrialShortPos.CalcPosValue(pCache)
// 			marginLevel, _, err := setting.FetchMarginLevel(tmpUserPos.Base, tmpUserPos.Quote, posValue)
// 			if err != nil {
// 				logger.Error(0, userAsset.TrialShortPos.ContractCode, userAsset.TrialShortPos.UserId, "PtGetFundCost FetchMarginLevel error:", err)
// 				return decimal.Zero
// 			}
//
// 			holdMargin := marginLevel.HoldingMarginRate.Mul(posValue)
// 			if userAsset.TrialShortPos.IsolatedMargin.GreaterThan(fundCost.Add(holdMargin)) {
// 				log.Println("PtGetTrialFundCost", userAsset.UserId, "cost", fundCost)
// 				userAsset.TrialShortPos.IsolatedMargin = userAsset.TrialShortPos.IsolatedMargin.Sub(fundCost)
// 				if userAsset.TrialShortPos.TrialMargin.GreaterThan(decimal.Zero) {
// 					userAsset.TrialShortPos.TrialMargin = userAsset.TrialShortPos.TrialMargin.Sub(fundCost)
// 				}
// 				bIsolatedParamList = append(bIsolatedParamList, bParam)
// 			}
//
// 		case enum.BothPos:
// 			posValue := userAsset.TrialBothPos.CalcPosValue(pCache)
// 			marginLevel, _, err := setting.FetchMarginLevel(tmpUserPos.Base, tmpUserPos.Quote, posValue)
// 			if err != nil {
// 				logger.Error(0, userAsset.TrialBothPos.ContractCode, userAsset.TrialBothPos.UserId, "PtGetTrialFundCost FetchMarginLevel error:", err)
// 				return decimal.Zero
// 			}
//
// 			holdMargin := marginLevel.HoldingMarginRate.Mul(posValue)
// 			if userAsset.TrialBothPos.IsolatedMargin.GreaterThan(fundCost.Add(holdMargin)) {
// 				log.Println("PtGetTrialFundCost", userAsset.UserId, "cost", fundCost)
// 				userAsset.TrialBothPos.IsolatedMargin = userAsset.TrialBothPos.IsolatedMargin.Sub(fundCost)
// 				if userAsset.TrialBothPos.TrialMargin.GreaterThan(decimal.Zero) {
// 					userAsset.TrialBothPos.TrialMargin = userAsset.TrialBothPos.TrialMargin.Sub(fundCost)
// 				}
// 				bIsolatedParamList = append(bIsolatedParamList, bParam)
// 			}
//
// 		default:
// 			loglib.GetLog().Info(fmt.Sprintf("PtGetTrialFundCost PosSide err.tmpUserPos:%+v,fundRate:%+v,pCache:%+v", tmpUserPos, fundRate, pCache))
// 			return decimal.Zero
//
// 		}
//
// 	default:
// 		loglib.GetLog().Info(fmt.Sprintf("PtGetTrialFundCost MarginMode err.tmpUserPos:%+v,fundRate:%+v", tmpUserPos, fundRate))
// 		return decimal.Zero
//
// 	}
//
// 	if len(bIsolatedParamList) > 0 {
// 		for _, param := range bIsolatedParamList {
// 			if !param.Amount.IsZero() {
// 				balanceRes := parameter.BalanceRes{
// 					AssetLogs:     make([]*entity.MqCmsAsset, 0),
// 					BillAssetLogs: make([]entity.BillAssetSync, 0),
// 				}
// 				userCache.OnTrialBalanceAdd(param, &balanceRes, param.Amount, param.Currency)
// 				for i := 0; i < len(balanceRes.BillAssetLogs); i++ {
// 					balanceRes.BillAssetLogs[i].MarkPrice = pCache.GetMarkPrice(tmpUserPos.ContractCode)
// 					balanceRes.BillAssetLogs[i].FundingRate = fundRate
// 				}
//
// 				err = userCache.UpdateTrialPos(userAsset)
// 				if err != nil {
// 					loglib.GetLog().Info(fmt.Sprintf("UpdateTrialPos err:%+v, tmpUserPos:%+v,fundRate:%+v,pCache:%+v,userAsset:%+v", err, tmpUserPos, fundRate, pCache, userAsset))
// 					return decimal.Zero
// 				}
// 				InsertLogFunding(tmpUserPos, *balanceRes.AssetLogs[0], userAsset, fundRate, pCache.GetMarkPrice(tmpUserPos.ContractCode), db)
//
// 				log.Println("PtGetTrialFundCost InsertLogFunding", userAsset.UserId, fundRate)
//
// 				go func() {
// 					// 推送 资金费用 账单
// 					redis := redislib.Redis()
// 					coupling.AddAssetLogs(redis, balanceRes.AssetLogs...)
// 					coupling.AddBills(redis, balanceRes.BillAssetLogs...)
// 				}()
// 				// 账本记录
// 				go func(p parameter.BalanceUpdate) {
// 					balance, err := swapcache.GetCurrencyTotalBalance(userAsset.UserId, p.Currency)
// 					if err != nil {
// 						loglib.GetLog().Errorf("PtGetTrialFundCost swapcache.GetCurrencyTotalBalance err: %v", err)
// 						return
// 					}
// 					if param.Amount.IsPositive() {
// 						es.SaveLedgerDetail(userAsset.UserId, p.Currency, es.DERIVATIVES_ACCOUNT_TYPE, es.FUND_FEE_LEDGER_TYPE, 0, p.Amount.Neg(), balance)
// 					} else {
// 						es.SaveLedgerDetail(userAsset.UserId, p.Currency, es.DERIVATIVES_ACCOUNT_TYPE, es.FUND_FEE_LEDGER_TYPE, 0, p.Amount, balance)
// 					}
// 				}(param)
// 			}
// 		}
// 	}
//
// 	return fundCost
// }
//
// // PtPutTrialFundCost 平台支付资金费用给体验金用户 资金费用取绝对值截取
// func PtPutTrialFundCost(tmpUserPos *entity.SettlementPos, fundRate, totalRealGet, totalInPos decimal.Decimal, pCache *price.PCache, db *gorm.DB) decimal.Decimal {
// 	settleId := util.GenerateId()
// 	fundCost := tmpUserPos.Pos.Mul(pCache.GetMarkPrice(tmpUserPos.ContractCode)).Mul(fundRate).Abs().Truncate(constvar.CurrencyPrecision)
// 	if !totalInPos.IsZero() {
// 		receivableCost := tmpUserPos.Pos.Div(totalInPos).Mul(totalRealGet).Abs().Truncate(constvar.CurrencyPrecision)
// 		// 有可能实际收取的不够，所以需要按比例重新计算下
// 		if receivableCost.Cmp(fundCost) < 0 {
// 			loglib.GetLog().Info(fmt.Sprintf("fund rate in receiv < real.receivableCost:%+v,fundCost:%+v,tmpUserPos:%+v", receivableCost, fundCost, tmpUserPos))
// 			fundCost = receivableCost
// 		}
// 	}
// 	userMutex := redislib.NewMutex(redisconst.MutexSwapPosLock+tmpUserPos.UserId, 30*time.Second)
// 	if userMutex.Lock() != nil {
// 		loglib.GetLog().Info(fmt.Sprintf("PtPutTrialFundCost lock err.tmpUserPos:%+v,fundRate:%+v,pCache:%+v", tmpUserPos, fundRate, pCache))
// 		return decimal.Zero
// 	}
// 	defer userMutex.Unlock()
//
// 	userCache := swapcache.NewPosCache(swapcache.CacheParam{
// 		TradeCommon: parameter.TradeCommon{
// 			Base:  tmpUserPos.Base,
// 			Quote: tmpUserPos.Quote,
// 		},
// 	}, tmpUserPos.UserId)
// 	userAsset, err := userCache.Load()
// 	if err != nil {
// 		loglib.GetLog().Info(fmt.Sprintf("PtPutTrialFundCost get asset err.userCache:%+v,tmpUserPos:%+v,fundRate:%+v,pCache:%+v", userCache, tmpUserPos, fundRate, pCache))
// 		return decimal.Zero
// 	}
//
// 	if tmpUserPos.MarginMode == 0 {
// 		switch tmpUserPos.PosSide {
// 		case enum.LongPos:
// 			tmpUserPos.MarginMode = userAsset.TrialLongPos.MarginMode
// 		case enum.ShortPos:
// 			tmpUserPos.MarginMode = userAsset.TrialShortPos.MarginMode
// 		case enum.BothPos:
// 			tmpUserPos.MarginMode = userAsset.TrialBothPos.MarginMode
// 		default:
// 			return decimal.Zero
// 		}
// 	}
//
// 	log.Println("PtPutTrialFundCost", "UserId", userAsset.UserId, "settleId", settleId, "Quote", userCache.Quote, "fundCost", fundCost)
//
// 	bIsolatedParamList := make([]parameter.BalanceUpdate, 0)
// 	bParam := userCache.FundingBalanceParam(settleId, userCache.Quote, fundCost)
// 	if enum.MarginMode(tmpUserPos.MarginMode) == enum.MarginModeIsolated {
// 		switch tmpUserPos.PosSide {
// 		case enum.LongPos:
// 			userAsset.TrialLongPos.IsolatedMargin = userAsset.TrialLongPos.IsolatedMargin.Add(fundCost)
// 			if userAsset.TrialLongPos.TrialMargin.GreaterThan(decimal.Zero) {
// 				userAsset.TrialLongPos.TrialMargin = userAsset.TrialLongPos.TrialMargin.Add(fundCost)
// 			}
// 			bIsolatedParamList = append(bIsolatedParamList, bParam)
//
// 		case enum.ShortPos:
// 			userAsset.TrialShortPos.IsolatedMargin = userAsset.TrialShortPos.IsolatedMargin.Add(fundCost)
// 			if userAsset.TrialShortPos.TrialMargin.GreaterThan(decimal.Zero) {
// 				userAsset.TrialShortPos.TrialMargin = userAsset.TrialShortPos.TrialMargin.Add(fundCost)
// 			}
// 			bIsolatedParamList = append(bIsolatedParamList, bParam)
//
// 		case enum.BothPos:
// 			userAsset.TrialBothPos.IsolatedMargin = userAsset.TrialBothPos.IsolatedMargin.Add(fundCost)
// 			if userAsset.TrialBothPos.TrialMargin.GreaterThan(decimal.Zero) {
// 				userAsset.TrialBothPos.TrialMargin = userAsset.TrialBothPos.TrialMargin.Add(fundCost)
// 			}
// 			bIsolatedParamList = append(bIsolatedParamList, bParam)
//
// 		default:
// 			loglib.GetLog().Info(fmt.Sprintf("PtPutTrialFundCost PosSide err.tmpUserPos:%+v,fundRate:%+v,pCache:%+v", tmpUserPos, fundRate, pCache))
// 			return decimal.Zero
// 		}
// 	}
//
// 	loglib.GetLog().Info(fmt.Sprintf("PtPutTrialFundCost FundingBalanceParam %s fundCost: %+v", settleId, fundCost))
// 	if len(bIsolatedParamList) > 0 {
// 		for _, param := range bIsolatedParamList {
// 			if param.Amount.IsZero() {
// 				continue
// 			}
// 			balanceRes := parameter.BalanceRes{
// 				AssetLogs:     make([]*entity.MqCmsAsset, 0),
// 				BillAssetLogs: make([]entity.BillAssetSync, 0),
// 			}
// 			userCache.OnTrialBalanceAdd(param, &balanceRes, param.Amount, param.Currency)
// 			for i := 0; i < len(balanceRes.BillAssetLogs); i++ {
// 				balanceRes.BillAssetLogs[i].MarkPrice = pCache.GetMarkPrice(tmpUserPos.ContractCode)
// 				balanceRes.BillAssetLogs[i].FundingRate = fundRate
// 			}
// 			err = userCache.UpdateTrialPos(userAsset)
// 			if err != nil {
// 				loglib.GetLog().Info(fmt.Sprintf("UpdateTrialPos err:%+v, tmpUserPos:%+v,fundRate:%+v,pCache:%+v,userAsset:%+v", err, tmpUserPos, fundRate, pCache, userAsset))
// 				return decimal.Zero
// 			}
// 			if len(balanceRes.AssetLogs) > 0 {
// 				for _, assetLog := range balanceRes.AssetLogs {
// 					InsertLogFunding(tmpUserPos, *assetLog, userAsset, fundRate, pCache.GetMarkPrice(tmpUserPos.ContractCode), db)
// 					log.Println("PtPutTrialFundCost InsertLogFunding", userAsset.UserId, fundRate)
// 				}
// 			}
// 			go func() {
// 				// 推送 资金费用 账单
// 				redis := redislib.Redis()
// 				coupling.AddAssetLogs(redis, balanceRes.AssetLogs...)
// 				coupling.AddBills(redis, balanceRes.BillAssetLogs...)
// 			}()
// 			// 账本统计
// 			go func(p parameter.BalanceUpdate) {
// 				balance, err := swapcache.GetCurrencyTotalBalance(userAsset.UserId, p.Currency)
// 				if err != nil {
// 					loglib.GetLog().Errorf("PtPutTrialFundCost swapcache.GetCurrencyTotalBalance err: %v", err)
// 					return
// 				}
// 				if p.Amount.IsPositive() {
// 					es.SaveLedgerDetail(userAsset.UserId, p.Currency, es.DERIVATIVES_ACCOUNT_TYPE, es.FUND_FEE_LEDGER_TYPE, 0, p.Amount, balance)
// 				} else {
// 					es.SaveLedgerDetail(userAsset.UserId, p.Currency, es.DERIVATIVES_ACCOUNT_TYPE, es.FUND_FEE_LEDGER_TYPE, 0, p.Amount.Neg(), balance)
// 				}
// 			}(param)
// 		}
// 	}
// 	return fundCost
// }
//
// func InsertLogFunding(tmpUserPos *entity.SettlementPos, mqAsset entity.MqCmsAsset, userAsset *entity.AssetSwap,
// 	fundRate, markPrice decimal.Decimal, db *gorm.DB,
// ) {
// 	logFunding := swap.LogFunding{
// 		FundingId:    mqAsset.OrderId,
// 		UserId:       tmpUserPos.UserId,
// 		Base:         tmpUserPos.Base,
// 		Quote:        tmpUserPos.Quote,
// 		ContractCode: tmpUserPos.ContractCode,
// 		FundingRate:     fundRate,
// 		MarkPrice:    markPrice,
// 		Amount:       mqAsset.Amount,
// 		FundPosNum:   tmpUserPos.Pos,
// 		MarginMode:   int(tmpUserPos.MarginMode),
// 		OperateTime:  mqAsset.OperateTime,
// 		PosSide:      int(tmpUserPos.PosSide),
// 	}
// 	if logFunding.Amount.IsPositive() {
// 		logFunding.Direction = enum.Income
// 	} else {
// 		logFunding.Direction = enum.Outlay
// 	}
// 	lFundPos := swap.FundPos{}
// 	sFundPos := swap.FundPos{}
// 	bFundPos := swap.FundPos{}
// 	if tmpUserPos.LongPos.IsPositive() {
// 		lFundPos.PosSide = int(enum.LongPos)
// 		lFundPos.Pos = tmpUserPos.LongPos
// 		lFundPos.OpenPriceAvg = tmpUserPos.LongPriceAvg.Truncate(constvar.CurrencyPrecision)
// 		lFundPos.Liquidation = userAsset.LongPos.Liquidation
// 		lFundPos.Margin = tmpUserPos.LongIsolatedMargin
// 		lFundPos.OpenTime = tmpUserPos.LongOpenTime / 1e9
// 		lFundPos.PosId = tmpUserPos.LongPosId
// 	}
// 	if tmpUserPos.ShortPos.IsPositive() {
// 		sFundPos.PosSide = int(enum.ShortPos)
// 		sFundPos.Pos = tmpUserPos.ShortPos
// 		sFundPos.OpenPriceAvg = tmpUserPos.ShortPriceAvg.Truncate(constvar.CurrencyPrecision)
// 		sFundPos.Liquidation = userAsset.ShortPos.Liquidation
// 		sFundPos.Margin = tmpUserPos.ShortIsolatedMargin
// 		sFundPos.OpenTime = tmpUserPos.ShortOpenTime / 1e9
// 		sFundPos.PosId = tmpUserPos.ShortPosId
// 	}
// 	if !tmpUserPos.BothPos.IsZero() {
// 		bFundPos.PosSide = int(enum.BothPos)
// 		bFundPos.Pos = tmpUserPos.BothPos.Abs()
// 		bFundPos.OpenPriceAvg = tmpUserPos.BothPriceAvg.Truncate(constvar.CurrencyPrecision)
// 		bFundPos.Liquidation = userAsset.BothPos.Liquidation
// 		bFundPos.Margin = tmpUserPos.BothIsolatedMargin
// 		bFundPos.OpenTime = tmpUserPos.BothOpenTime / 1e9
// 		bFundPos.PosId = tmpUserPos.BothPosId
// 	}
//
// 	switch tmpUserPos.PosSide {
// 	case enum.LongPos:
// 		if tmpUserPos.IsTrial {
// 			// 体验金仓位
// 			logFunding.UserType = int(userAsset.TrialLongPos.UserType)
// 			logFunding.AccountType = userAsset.TrialLongPos.AccountType
// 			logFunding.Leverage = userAsset.TrialLongPos.Leverage
// 		} else {
// 			// 真实仓位
// 			logFunding.UserType = int(userAsset.LongPos.UserType)
// 			logFunding.AccountType = userAsset.LongPos.AccountType
// 			logFunding.Leverage = userAsset.LongPos.Leverage
// 		}
//
// 	case enum.ShortPos:
// 		if tmpUserPos.IsTrial {
// 			// 体验金仓位
// 			logFunding.UserType = int(userAsset.TrialShortPos.UserType)
// 			logFunding.AccountType = userAsset.TrialShortPos.AccountType
// 			logFunding.Leverage = userAsset.ShortPos.Leverage
// 		} else {
// 			// 真实仓位
// 			logFunding.UserType = int(userAsset.ShortPos.UserType)
// 			logFunding.AccountType = userAsset.ShortPos.AccountType
// 			logFunding.Leverage = userAsset.ShortPos.Leverage
// 		}
//
// 	case enum.BothPos:
// 		if tmpUserPos.IsTrial {
// 			// 体验金仓位
// 			logFunding.UserType = int(userAsset.TrialBothPos.UserType)
// 			logFunding.AccountType = userAsset.TrialBothPos.AccountType
// 			logFunding.Leverage = userAsset.TrialBothPos.Leverage
// 		} else {
// 			// 真实仓位
// 			logFunding.UserType = int(userAsset.BothPos.UserType)
// 			logFunding.AccountType = userAsset.BothPos.AccountType
// 			logFunding.Leverage = userAsset.BothPos.Leverage
// 		}
//
// 	default:
//
// 	}
//
// 	if tmpUserPos.MarginMode == int32(enum.MarginModeIsolated) {
// 		switch tmpUserPos.PosSide {
// 		case enum.LongPos:
// 			logFunding.PosData = append(logFunding.PosData, lFundPos)
//
// 		case enum.ShortPos:
// 			logFunding.PosData = append(logFunding.PosData, sFundPos)
//
// 		case enum.BothPos:
// 			logFunding.PosData = append(logFunding.PosData, bFundPos)
//
// 		default:
//
// 		}
// 	} else {
// 		lever := decimal.NewFromInt(int64(logFunding.Leverage))
// 		if lFundPos.Pos.IsPositive() {
// 			if lFundPos.Margin.IsZero() && lever.IsPositive() {
// 				lFundPos.Margin = lFundPos.Pos.Mul(markPrice).Div(lever).Truncate(constvar.CurrencyPrecision)
// 			}
// 			logFunding.PosData = append(logFunding.PosData, lFundPos)
// 		}
// 		if sFundPos.Pos.IsPositive() {
// 			if sFundPos.Margin.IsZero() && lever.IsPositive() {
// 				sFundPos.Margin = sFundPos.Pos.Mul(markPrice).Div(lever).Truncate(constvar.CurrencyPrecision)
// 			}
// 			logFunding.PosData = append(logFunding.PosData, sFundPos)
// 		}
// 		if !bFundPos.Pos.IsZero() {
// 			if bFundPos.Margin.IsZero() && lever.IsPositive() {
// 				bFundPos.Margin = bFundPos.Pos.Mul(markPrice).Div(lever).Truncate(constvar.CurrencyPrecision)
// 			}
// 			logFunding.PosData = append(logFunding.PosData, bFundPos)
// 		}
// 	}
//
// 	posB, _ := json.Marshal(logFunding.PosData)
// 	logFunding.StrPos = string(posB)
//
// 	err := logFunding.Insert(db)
// 	if err != nil {
// 		loglib.GetLog().Error(fmt.Sprintf("log funding insert err:%+v", err))
// 	}
// }
