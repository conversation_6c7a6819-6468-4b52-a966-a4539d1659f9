env:
  serviceName: 'cnx-futures-asset'
  log:
    pretty: true
    level: info
    path: ./logs/log
    maxAge: 72h
    rotationTime: 1h
  debug: true

http:
  port: 8080
  timeouts:
    readTimeout: 2s
    readHeaderTimeout: 2s
    writeTimeout: 10s
    idleTimeout: 5s

observability:
  pyroscope:
    enable: false
    url: 'http://pyroscope'
  otel:
    enable: false
    host: 127.0.0.1
    port: 4317
    isSecure: false
    exporter: otlp

db:
  logLevel: warn
  master:
    host: 127.0.0.1
    port: 3306
    username: root
    password: yytt
    name: cnx_futures_asset
    maxIdleConns: 10
    maxOpenConns: 200
    connMaxLifetime: 10s

redis:
  host: 127.0.0.1
  port: 6379
  username:
  password:
  dialTimeout: 5s
  readTimeout: 3s
  writeTimeout: 3s
  poolSize: 10
  minIdleConns: 2
  maxIdleConns: 10
  connMaxIdleTime: 30s

elasticsearch:
  username:
  password:
  maxIdleConnsPerHost: 50
  dialerTimeout: 30s
  idleConnTimeout: 1h
  TLSHandshakeTimeout: 10s
  responseHeaderTimeout: 30s
  expectContinueTimeout: 1s
  routineSize: 3
  routineChanSize: 10
  url:
    - http://127.0.0.1:9200

mongodb:
  hosts: [127.0.0.1:27017]
  username:
  password:
  databaseName: 'esc_setting'
  minPoolSize: 10
  maxPoolSize: 20
  maxConnIdleTime: 30m

kafka:
  clientID: cnx-futures-asset
  brokers:
    - kafka-1.cubnet-depencese.orb.local:9092
    - kafka-2.cubnet-depencese.orb.local:9092
    - kafka-3.cubnet-depencese.orb.local:9092
  sasl:
    enable: false
    user: user
    password: 1234
  channelBufferSize: 256
  producer:
    topic:
      assetsbill: fasset-assetsbill:16:3
      optionbill: fasset-optionbill:3:3
      fundingfee: fasset-fundingfee:3:3
      fundingrate: fasset-fundingrate:3:3

customConfig:
  grpc:
    port: 8081

  memberService:
    url: http://cnx-member-dev
    token:
  futuresEngineService:
    url: http://cnx-futures-engine-dev
    token:
  futuresOrderService:
    url: http://cnx-futures-order-dev
    token:
  futuresKLineService:
    url: http://cnx-futures-kline-dev
    token:
  notificationService:
    url: http://cnx-notification-dev
    token:
  settingService:
    url: http://cnx-setting-dev
    token:
  walletService:
    url: http://cnx-wallet-dev
    token:
  auditLogService:
    url: http://cnx-audit-log-dev
    token:
  approvalService:
    url: http://cnx-approval-dev
    token:
  commissionService:
    url: http://cnx-commission-dev
    token:
  lotusFingerService:
    url: http://cnx-lotus-finger-dev
    token:
  campaignService:
    url: http://cnx-campaign-dev
    token:

  httpClientTransportConfig:
    idleConnTimeout: 90 # 這裡會乘上 time.Second
    tLSHandshakeTimeout: 30 # 這裡會乘上 time.Second
    expectContinueTimeout: 1 # 這裡會乘上 time.Second
    responseHeaderTimeout: 30 # 這裡會乘上 time.Second
    maxIdleConns: 5000
    maxIdleConnsPerHost: 10000
    maxConnsPerHost: 0
    netDialerTimeOut: 60 # 這裡會乘上 time.Second
    netDialerKeepAlive: 60 # 這裡會乘上 time.Second
